#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成多语言语料库分析对比可视化图表
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_word_frequency_comparison():
    """创建词频分析对比图表"""
    # 词频数据
    data = {
        '语言': ['中文', '中文', '中文', '英文', '英文', '英文', '德文'],
        '库名称': ['jieba', 'spaCy', 'NLTK', 'NLTK', 'spaCy', 'TextBlob', 'NLTK'],
        '词汇总数': [15506, 46281, 46278, 866, 906, 874, 13628],
        '最高频率': [4205, 3100, 3107, 45, 45, 45, 930],
        '平均频率': [12.61, 5.00, 5.00, 1.75, 1.67, 1.76, 4.99]
    }
    
    df = pd.DataFrame(data)
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('多语言语料库分析词频对比', fontsize=16, fontweight='bold')
    
    # 1. 词汇总数对比
    ax1 = axes[0, 0]
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    bars1 = ax1.bar(range(len(df)), df['词汇总数'], color=colors)
    ax1.set_title('词汇识别总数对比')
    ax1.set_xlabel('库-语言组合')
    ax1.set_ylabel('词汇总数')
    ax1.set_xticks(range(len(df)))
    ax1.set_xticklabels([f"{row['语言']}-{row['库名称']}" for _, row in df.iterrows()], rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars1, df['词汇总数']):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:,}', ha='center', va='bottom', fontsize=9)
    
    # 2. 最高频率对比
    ax2 = axes[0, 1]
    bars2 = ax2.bar(range(len(df)), df['最高频率'], color=colors)
    ax2.set_title('最高词频对比')
    ax2.set_xlabel('库-语言组合')
    ax2.set_ylabel('最高频率')
    ax2.set_xticks(range(len(df)))
    ax2.set_xticklabels([f"{row['语言']}-{row['库名称']}" for _, row in df.iterrows()], rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars2, df['最高频率']):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontsize=9)
    
    # 3. 平均频率对比
    ax3 = axes[1, 0]
    bars3 = ax3.bar(range(len(df)), df['平均频率'], color=colors)
    ax3.set_title('平均词频对比')
    ax3.set_xlabel('库-语言组合')
    ax3.set_ylabel('平均频率')
    ax3.set_xticks(range(len(df)))
    ax3.set_xticklabels([f"{row['语言']}-{row['库名称']}" for _, row in df.iterrows()], rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars3, df['平均频率']):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.2f}', ha='center', va='bottom', fontsize=9)
    
    # 4. 综合评分对比（改为柱状图）
    ax4 = axes[1, 1]

    # 计算综合评分（标准化）
    df['词汇总数_标准化'] = df['词汇总数'] / df['词汇总数'].max() * 10
    df['最高频率_标准化'] = df['最高频率'] / df['最高频率'].max() * 10
    df['平均频率_标准化'] = df['平均频率'] / df['平均频率'].max() * 10

    # 计算综合评分
    df['综合评分'] = (df['词汇总数_标准化'] + df['最高频率_标准化'] + df['平均频率_标准化']) / 3

    # 选择推荐的库
    recommended_libs = ['中文-jieba', '英文-NLTK', '德文-NLTK']
    recommended_data = df[df.apply(lambda x: f"{x['语言']}-{x['库名称']}" in recommended_libs, axis=1)]

    if not recommended_data.empty:
        bars4 = ax4.bar(range(len(recommended_data)), recommended_data['综合评分'],
                       color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
        ax4.set_title('推荐库综合评分对比')
        ax4.set_xlabel('推荐库')
        ax4.set_ylabel('综合评分')
        ax4.set_xticks(range(len(recommended_data)))
        ax4.set_xticklabels([f"{row['语言']}-{row['库名称']}" for _, row in recommended_data.iterrows()])

        # 添加数值标签
        for bar, value in zip(bars4, recommended_data['综合评分']):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.2f}', ha='center', va='bottom', fontsize=10)
    else:
        ax4.text(0.5, 0.5, '推荐库数据不足', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('推荐库综合评分对比')
    
    plt.tight_layout()
    plt.savefig('词频分析对比图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_kwic_comparison():
    """创建KWIC分析对比图表"""
    # KWIC数据
    kwic_data = {
        '语言-库': ['中文-jieba', '中文-spaCy', '英文-NLTK', '英文-spaCy', '英文-TextBlob', '德文-NLTK'],
        '匹配数量': [2401, 25, 45, 45, 43, 528],
        '效率等级': ['高', '中', '中', '中', '中', '高']
    }
    
    df_kwic = pd.DataFrame(kwic_data)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('KWIC分析功能对比', fontsize=16, fontweight='bold')
    
    # 1. 匹配数量对比
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    bars = ax1.bar(range(len(df_kwic)), df_kwic['匹配数量'], color=colors)
    ax1.set_title('KWIC匹配数量对比')
    ax1.set_xlabel('库-语言组合')
    ax1.set_ylabel('匹配数量')
    ax1.set_xticks(range(len(df_kwic)))
    ax1.set_xticklabels(df_kwic['语言-库'], rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars, df_kwic['匹配数量']):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value}', ha='center', va='bottom', fontsize=10)
    
    # 2. 效率等级分布
    efficiency_counts = df_kwic['效率等级'].value_counts()
    ax2.pie(efficiency_counts.values, labels=efficiency_counts.index, autopct='%1.1f%%',
            colors=['#FF6B6B', '#4ECDC4'], startangle=90)
    ax2.set_title('KWIC分析效率等级分布')
    
    plt.tight_layout()
    plt.savefig('KWIC分析对比图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_completeness_comparison():
    """创建功能完整性对比图表"""
    # 完整性数据
    completeness_data = {
        '语言': ['中文', '中文', '中文', '英文', '英文', '英文', '德文', '德文'],
        '库名称': ['jieba', 'NLTK', 'spaCy', 'NLTK', 'spaCy', 'TextBlob', 'NLTK', 'spaCy'],
        '完成功能数': [5, 3, 5, 5, 5, 5, 5, 5],
        '总功能数': [6, 6, 6, 6, 6, 6, 6, 6],
        '完整率': [83.3, 50.0, 83.3, 83.3, 83.3, 83.3, 83.3, 83.3]
    }
    
    df_comp = pd.DataFrame(completeness_data)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('功能完整性对比分析', fontsize=16, fontweight='bold')
    
    # 1. 完整率对比
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    bars = ax1.bar(range(len(df_comp)), df_comp['完整率'], color=colors)
    ax1.set_title('功能完整率对比')
    ax1.set_xlabel('库-语言组合')
    ax1.set_ylabel('完整率 (%)')
    ax1.set_xticks(range(len(df_comp)))
    ax1.set_xticklabels([f"{row['语言']}-{row['库名称']}" for _, row in df_comp.iterrows()], rotation=45)
    ax1.set_ylim(0, 100)
    
    # 添加数值标签
    for bar, value in zip(bars, df_comp['完整率']):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 2,
                f'{value:.1f}%', ha='center', va='bottom', fontsize=9)
    
    # 添加基准线
    ax1.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='优秀标准线(80%)')
    ax1.legend()
    
    # 2. 各语言库完整性热力图
    pivot_data = df_comp.pivot(index='语言', columns='库名称', values='完整率')
    
    sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlGn', 
                ax=ax2, cbar_kws={'label': '完整率 (%)'})
    ax2.set_title('各语言库功能完整性热力图')
    ax2.set_xlabel('库名称')
    ax2.set_ylabel('语言')
    
    plt.tight_layout()
    plt.savefig('功能完整性对比图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_recommendation_summary():
    """创建推荐总结图表"""
    # 推荐数据
    recommendations = {
        '语言': ['中文', '英文', '德文'],
        '首选库': ['jieba', 'NLTK', 'NLTK'],
        '推荐指数': [5, 5, 5],
        '词汇识别能力': [4, 4, 5],
        'AntConc相似度': [3, 5, 5],
        '功能完整性': [5, 5, 5],
        '易用性': [5, 4, 4]
    }
    
    df_rec = pd.DataFrame(recommendations)
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))
    
    categories = ['词汇识别能力', 'AntConc相似度', '功能完整性', '易用性']
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for idx, (_, row) in enumerate(df_rec.iterrows()):
        values = [row['词汇识别能力'], row['AntConc相似度'], row['功能完整性'], row['易用性']]
        values += values[:1]
        
        ax.plot(angles, values, 'o-', linewidth=2, label=f"{row['语言']}-{row['首选库']}", color=colors[idx])
        ax.fill(angles, values, alpha=0.25, color=colors[idx])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 5)
    ax.set_yticks([1, 2, 3, 4, 5])
    ax.set_yticklabels(['1', '2', '3', '4', '5'])
    ax.grid(True)
    
    plt.title('最终推荐库综合评估雷达图', size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('推荐总结雷达图.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("开始生成对比可视化图表...")
    
    # 创建各种对比图表
    print("1. 生成词频分析对比图表...")
    create_word_frequency_comparison()
    
    print("2. 生成KWIC分析对比图表...")
    create_kwic_comparison()
    
    print("3. 生成功能完整性对比图表...")
    create_completeness_comparison()
    
    print("4. 生成推荐总结雷达图...")
    create_recommendation_summary()
    
    print("\n所有可视化图表已生成完成！")
    print("生成的图表文件：")
    print("- 词频分析对比图表.png")
    print("- KWIC分析对比图表.png") 
    print("- 功能完整性对比图表.png")
    print("- 推荐总结雷达图.png")

if __name__ == "__main__":
    main()
