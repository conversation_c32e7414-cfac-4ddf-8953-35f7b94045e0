#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多语言语料库分析对比报告生成器
对比各种Python库的分析结果与AntConc分析结果的相似度
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import spearmanr, pearsonr
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CorpusAnalysisComparator:
    """语料库分析结果对比器"""
    
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.results = {}
        
    def load_antconc_results(self, language):
        """加载AntConc分析结果"""
        if language == "中文":
            antconc_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "1.Word_results.txt"
        elif language == "英文":
            antconc_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "1.词频.txt"
        elif language == "德文":
            antconc_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "1.Word_results.txt"
        else:
            raise ValueError(f"不支持的语言: {language}")
            
        try:
            # 读取AntConc结果文件
            with open(antconc_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 解析数据
            data = []
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 4:
                        word = parts[2] if len(parts) > 2 else parts[0]
                        rank = int(parts[3]) if len(parts) > 3 and parts[3].isdigit() else 0
                        freq = int(parts[4]) if len(parts) > 4 and parts[4].isdigit() else 0
                        if word and freq > 0:
                            data.append({'word': word, 'rank': rank, 'frequency': freq})
            
            return pd.DataFrame(data)
        except Exception as e:
            print(f"加载{language}AntConc结果时出错: {e}")
            return pd.DataFrame()
    
    def load_library_results(self, language, library):
        """加载Python库分析结果"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_word_frequency_report.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_word_frequency_report.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_word_frequency_report.csv"
        else:
            raise ValueError(f"不支持的语言或库: {language}, {library}")
            
        try:
            df = pd.read_csv(csv_path)
            # 标准化列名
            if 'Word' in df.columns:
                df = df.rename(columns={'Word': 'word', 'Frequency': 'frequency', 'Rank': 'rank'})
            elif 'word' in df.columns:
                pass  # 已经是标准格式
            else:
                print(f"未找到标准列名在文件: {csv_path}")
                return pd.DataFrame()
                
            return df[['word', 'frequency']].copy()
        except Exception as e:
            print(f"加载{language}-{library}结果时出错: {e}")
            return pd.DataFrame()
    
    def calculate_similarity_metrics(self, antconc_df, library_df, top_n=50):
        """计算相似度指标"""
        if antconc_df.empty or library_df.empty:
            return {}
            
        # 获取前N个词汇
        antconc_top = antconc_df.head(top_n)
        library_top = library_df.head(top_n)
        
        # 计算词汇重叠率
        antconc_words = set(antconc_top['word'].str.lower())
        library_words = set(library_top['word'].str.lower())
        
        overlap = len(antconc_words.intersection(library_words))
        overlap_rate = overlap / len(antconc_words) if len(antconc_words) > 0 else 0
        
        # 合并数据进行频率对比
        merged = pd.merge(antconc_top, library_top, on='word', how='inner', suffixes=('_antconc', '_lib'))
        
        if len(merged) > 1:
            # 计算相关系数
            spearman_corr, _ = spearmanr(merged['frequency_antconc'], merged['frequency_lib'])
            pearson_corr, _ = pearsonr(merged['frequency_antconc'], merged['frequency_lib'])
            
            # 计算RMSE
            rmse = np.sqrt(mean_squared_error(merged['frequency_antconc'], merged['frequency_lib']))
        else:
            spearman_corr = pearson_corr = rmse = 0
        
        return {
            'overlap_rate': overlap_rate,
            'overlap_count': overlap,
            'spearman_correlation': spearman_corr,
            'pearson_correlation': pearson_corr,
            'rmse': rmse,
            'common_words_count': len(merged)
        }
    
    def compare_all_languages(self):
        """对比所有语言的分析结果"""
        languages_libraries = {
            "中文": ["jieba", "nltk", "spacy"],
            "英文": ["nltk", "spacy", "textblob"],
            "德文": ["nltk", "spacy"]  # stanza太慢暂时不比较
        }
        
        comparison_results = {}
        
        for language, libraries in languages_libraries.items():
            print(f"\n正在分析{language}...")
            
            # 加载AntConc结果
            antconc_df = self.load_antconc_results(language)
            if antconc_df.empty:
                print(f"无法加载{language}的AntConc结果")
                continue
                
            language_results = {}
            
            for library in libraries:
                print(f"  对比{library}库...")
                
                # 加载库结果
                library_df = self.load_library_results(language, library)
                if library_df.empty:
                    print(f"    无法加载{library}结果")
                    continue
                
                # 计算相似度
                metrics = self.calculate_similarity_metrics(antconc_df, library_df)
                language_results[library] = metrics
                
                print(f"    词汇重叠率: {metrics['overlap_rate']:.3f}")
                print(f"    Spearman相关系数: {metrics['spearman_correlation']:.3f}")
            
            comparison_results[language] = language_results
        
        return comparison_results
    
    def generate_comparison_report(self, results):
        """生成对比报告"""
        report = []
        report.append("# 多语言语料库分析结果对比报告\n")
        report.append("## 各库与AntConc分析结果相似度对比\n")
        
        # 创建汇总表格
        summary_data = []
        
        for language, libraries in results.items():
            report.append(f"### {language}分析结果对比\n")
            
            if not libraries:
                report.append("无可用的对比数据\n")
                continue
            
            # 按重叠率排序
            sorted_libs = sorted(libraries.items(), key=lambda x: x[1]['overlap_rate'], reverse=True)
            
            report.append("| 库名称 | 词汇重叠率 | Spearman相关系数 | Pearson相关系数 | 共同词汇数 |\n")
            report.append("|--------|------------|------------------|-----------------|------------|\n")
            
            for lib_name, metrics in sorted_libs:
                overlap_rate = metrics['overlap_rate']
                spearman = metrics['spearman_correlation']
                pearson = metrics['pearson_correlation']
                common_words = metrics['common_words_count']
                
                report.append(f"| {lib_name} | {overlap_rate:.3f} | {spearman:.3f} | {pearson:.3f} | {common_words} |\n")
                
                # 添加到汇总数据
                summary_data.append({
                    'language': language,
                    'library': lib_name,
                    'overlap_rate': overlap_rate,
                    'spearman_correlation': spearman,
                    'recommendation_score': overlap_rate * 0.6 + abs(spearman) * 0.4
                })
            
            # 推荐最佳库
            best_lib = sorted_libs[0]
            report.append(f"\n**推荐库**: {best_lib[0]} (词汇重叠率: {best_lib[1]['overlap_rate']:.3f})\n\n")
        
        # 总体推荐
        report.append("## 总体推荐\n")
        
        for language in results.keys():
            lang_data = [item for item in summary_data if item['language'] == language]
            if lang_data:
                best = max(lang_data, key=lambda x: x['recommendation_score'])
                report.append(f"- **{language}**: 推荐使用 **{best['library']}** 库\n")
        
        return ''.join(report), summary_data

    def create_visualization(self, summary_data):
        """创建可视化图表"""
        if not summary_data:
            return

        df = pd.DataFrame(summary_data)

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('多语言语料库分析结果对比', fontsize=16, fontweight='bold')

        # 1. 词汇重叠率对比
        ax1 = axes[0, 0]
        pivot_overlap = df.pivot(index='language', columns='library', values='overlap_rate')
        sns.heatmap(pivot_overlap, annot=True, fmt='.3f', cmap='YlOrRd', ax=ax1)
        ax1.set_title('词汇重叠率对比')
        ax1.set_xlabel('库名称')
        ax1.set_ylabel('语言')

        # 2. Spearman相关系数对比
        ax2 = axes[0, 1]
        pivot_spearman = df.pivot(index='language', columns='library', values='spearman_correlation')
        sns.heatmap(pivot_spearman, annot=True, fmt='.3f', cmap='RdYlBu_r', ax=ax2)
        ax2.set_title('Spearman相关系数对比')
        ax2.set_xlabel('库名称')
        ax2.set_ylabel('语言')

        # 3. 综合推荐分数
        ax3 = axes[1, 0]
        for language in df['language'].unique():
            lang_data = df[df['language'] == language]
            ax3.bar([f"{language}-{lib}" for lib in lang_data['library']],
                   lang_data['recommendation_score'],
                   label=language, alpha=0.7)
        ax3.set_title('综合推荐分数')
        ax3.set_xlabel('语言-库组合')
        ax3.set_ylabel('推荐分数')
        ax3.tick_params(axis='x', rotation=45)
        ax3.legend()

        # 4. 各语言最佳库推荐
        ax4 = axes[1, 1]
        best_libs = df.loc[df.groupby('language')['recommendation_score'].idxmax()]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        bars = ax4.bar(best_libs['language'], best_libs['recommendation_score'], color=colors)
        ax4.set_title('各语言最佳库推荐')
        ax4.set_xlabel('语言')
        ax4.set_ylabel('推荐分数')

        # 在柱状图上添加库名称
        for i, (bar, lib) in enumerate(zip(bars, best_libs['library'])):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    lib, ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('多语言分析对比结果.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    print("开始多语言语料库分析结果对比...")

    # 创建对比器
    comparator = CorpusAnalysisComparator()

    # 执行对比分析
    results = comparator.compare_all_languages()

    # 生成报告
    report_text, summary_data = comparator.generate_comparison_report(results)

    # 保存报告
    report_path = "多语言分析对比报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_text)

    print(f"\n对比报告已保存到: {report_path}")

    # 创建可视化
    if summary_data:
        comparator.create_visualization(summary_data)

    # 打印报告内容
    print("\n" + "="*50)
    print(report_text)

if __name__ == "__main__":
    main()
