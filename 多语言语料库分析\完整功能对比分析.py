#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能对比分析 - 基于实际数据的六大功能全面对比
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CompleteFeatureComparator:
    """完整功能对比分析器"""
    
    def __init__(self):
        self.base_dir = Path(".")
        
    def load_csv_safely(self, file_path):
        """安全加载CSV文件"""
        try:
            if file_path.exists():
                return pd.read_csv(file_path, encoding='utf-8')
            else:
                print(f"文件不存在: {file_path}")
                return pd.DataFrame()
        except Exception as e:
            print(f"加载文件出错: {e}")
            return pd.DataFrame()
    
    def analyze_word_frequency_comprehensive(self):
        """全面分析词频功能"""
        print("="*60)
        print("1. 词频分析功能全面对比")
        print("="*60)
        
        results = {}
        
        # 中文词频分析
        print("\n【中文词频分析对比】")
        chinese_results = {}
        
        # jieba
        jieba_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_word_frequency_report.csv"
        jieba_df = self.load_csv_safely(jieba_path)
        if not jieba_df.empty:
            chinese_results['jieba'] = {
                'total_words': len(jieba_df),
                'max_frequency': jieba_df['Frequency'].max() if 'Frequency' in jieba_df.columns else 0,
                'min_frequency': jieba_df['Frequency'].min() if 'Frequency' in jieba_df.columns else 0,
                'mean_frequency': jieba_df['Frequency'].mean() if 'Frequency' in jieba_df.columns else 0,
                'top_10_words': jieba_df.head(10)['Word'].tolist() if 'Word' in jieba_df.columns else []
            }
            print(f"jieba: {len(jieba_df)}个词汇, 最高频率: {jieba_df['Frequency'].max()}")
        
        # spacy
        spacy_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
        spacy_df = self.load_csv_safely(spacy_path)
        if not spacy_df.empty:
            chinese_results['spacy'] = {
                'total_words': len(spacy_df),
                'max_frequency': spacy_df['Frequency'].max() if 'Frequency' in spacy_df.columns else 0,
                'min_frequency': spacy_df['Frequency'].min() if 'Frequency' in spacy_df.columns else 0,
                'mean_frequency': spacy_df['Frequency'].mean() if 'Frequency' in spacy_df.columns else 0,
                'top_10_words': spacy_df.head(10)['Word'].tolist() if 'Word' in spacy_df.columns else []
            }
            print(f"spacy: {len(spacy_df)}个词汇, 最高频率: {spacy_df['Frequency'].max()}")
        
        # nltk
        nltk_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
        nltk_df = self.load_csv_safely(nltk_path)
        if not nltk_df.empty:
            chinese_results['nltk'] = {
                'total_words': len(nltk_df),
                'max_frequency': nltk_df['Frequency'].max() if 'Frequency' in nltk_df.columns else 0,
                'min_frequency': nltk_df['Frequency'].min() if 'Frequency' in nltk_df.columns else 0,
                'mean_frequency': nltk_df['Frequency'].mean() if 'Frequency' in nltk_df.columns else 0,
                'top_10_words': nltk_df.head(10)['Word'].tolist() if 'Word' in nltk_df.columns else []
            }
            print(f"nltk: {len(nltk_df)}个词汇, 最高频率: {nltk_df['Frequency'].max()}")
        
        results['中文'] = chinese_results
        
        # 英文词频分析
        print("\n【英文词频分析对比】")
        english_results = {}
        
        # nltk
        nltk_en_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
        nltk_en_df = self.load_csv_safely(nltk_en_path)
        if not nltk_en_df.empty:
            english_results['nltk'] = {
                'total_words': len(nltk_en_df),
                'max_frequency': nltk_en_df['Frequency'].max() if 'Frequency' in nltk_en_df.columns else 0,
                'min_frequency': nltk_en_df['Frequency'].min() if 'Frequency' in nltk_en_df.columns else 0,
                'mean_frequency': nltk_en_df['Frequency'].mean() if 'Frequency' in nltk_en_df.columns else 0,
                'top_10_words': nltk_en_df.head(10)['Word'].tolist() if 'Word' in nltk_en_df.columns else []
            }
            print(f"nltk: {len(nltk_en_df)}个词汇, 最高频率: {nltk_en_df['Frequency'].max()}")
        
        # spacy
        spacy_en_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
        spacy_en_df = self.load_csv_safely(spacy_en_path)
        if not spacy_en_df.empty:
            english_results['spacy'] = {
                'total_words': len(spacy_en_df),
                'max_frequency': spacy_en_df['Frequency'].max() if 'Frequency' in spacy_en_df.columns else 0,
                'min_frequency': spacy_en_df['Frequency'].min() if 'Frequency' in spacy_en_df.columns else 0,
                'mean_frequency': spacy_en_df['Frequency'].mean() if 'Frequency' in spacy_en_df.columns else 0,
                'top_10_words': spacy_en_df.head(10)['Word'].tolist() if 'Word' in spacy_en_df.columns else []
            }
            print(f"spacy: {len(spacy_en_df)}个词汇, 最高频率: {spacy_en_df['Frequency'].max()}")
        
        # textblob
        textblob_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_word_frequency_report.csv"
        textblob_df = self.load_csv_safely(textblob_path)
        if not textblob_df.empty:
            english_results['textblob'] = {
                'total_words': len(textblob_df),
                'max_frequency': textblob_df['Frequency'].max() if 'Frequency' in textblob_df.columns else 0,
                'min_frequency': textblob_df['Frequency'].min() if 'Frequency' in textblob_df.columns else 0,
                'mean_frequency': textblob_df['Frequency'].mean() if 'Frequency' in textblob_df.columns else 0,
                'top_10_words': textblob_df.head(10)['Word'].tolist() if 'Word' in textblob_df.columns else []
            }
            print(f"textblob: {len(textblob_df)}个词汇, 最高频率: {textblob_df['Frequency'].max()}")
        
        results['英文'] = english_results
        
        # 德文词频分析
        print("\n【德文词频分析对比】")
        german_results = {}
        
        # nltk
        nltk_de_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_word_frequency_report.csv"
        nltk_de_df = self.load_csv_safely(nltk_de_path)
        if not nltk_de_df.empty:
            german_results['nltk'] = {
                'total_words': len(nltk_de_df),
                'max_frequency': nltk_de_df['Frequency'].max() if 'Frequency' in nltk_de_df.columns else 0,
                'min_frequency': nltk_de_df['Frequency'].min() if 'Frequency' in nltk_de_df.columns else 0,
                'mean_frequency': nltk_de_df['Frequency'].mean() if 'Frequency' in nltk_de_df.columns else 0,
                'top_10_words': nltk_de_df.head(10)['Word'].tolist() if 'Word' in nltk_de_df.columns else []
            }
            print(f"nltk: {len(nltk_de_df)}个词汇, 最高频率: {nltk_de_df['Frequency'].max()}")
        
        # spacy
        spacy_de_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_word_frequency_report.csv"
        spacy_de_df = self.load_csv_safely(spacy_de_path)
        if not spacy_de_df.empty:
            german_results['spacy'] = {
                'total_words': len(spacy_de_df),
                'max_frequency': spacy_de_df['Frequency'].max() if 'Frequency' in spacy_de_df.columns else 0,
                'min_frequency': spacy_de_df['Frequency'].min() if 'Frequency' in spacy_de_df.columns else 0,
                'mean_frequency': spacy_de_df['Frequency'].mean() if 'Frequency' in spacy_de_df.columns else 0,
                'top_10_words': spacy_de_df.head(10)['Word'].tolist() if 'Word' in spacy_de_df.columns else []
            }
            print(f"spacy: {len(spacy_de_df)}个词汇, 最高频率: {spacy_de_df['Frequency'].max()}")
        
        results['德文'] = german_results
        
        return results
    
    def analyze_kwic_comprehensive(self):
        """全面分析KWIC功能"""
        print("\n" + "="*60)
        print("2. KWIC分析功能全面对比")
        print("="*60)
        
        results = {}
        
        # 中文KWIC分析
        print("\n【中文KWIC分析对比】")
        chinese_kwic = {}
        
        # jieba
        jieba_kwic_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_KWIC_人工智能.csv"
        jieba_kwic_df = self.load_csv_safely(jieba_kwic_path)
        if not jieba_kwic_df.empty:
            chinese_kwic['jieba'] = {
                'total_matches': len(jieba_kwic_df),
                'context_variety': len(jieba_kwic_df['Left_Context'].unique()) if 'Left_Context' in jieba_kwic_df.columns else 0
            }
            print(f"jieba KWIC: {len(jieba_kwic_df)}个匹配")
        
        # spacy
        spacy_kwic_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_KWIC_人工智能.csv"
        spacy_kwic_df = self.load_csv_safely(spacy_kwic_path)
        if not spacy_kwic_df.empty:
            chinese_kwic['spacy'] = {
                'total_matches': len(spacy_kwic_df),
                'context_variety': len(spacy_kwic_df['Left_Context'].unique()) if 'Left_Context' in spacy_kwic_df.columns else 0
            }
            print(f"spacy KWIC: {len(spacy_kwic_df)}个匹配")
        
        results['中文'] = chinese_kwic
        
        # 英文KWIC分析
        print("\n【英文KWIC分析对比】")
        english_kwic = {}
        
        # nltk
        nltk_kwic_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_KWIC_china.csv"
        nltk_kwic_df = self.load_csv_safely(nltk_kwic_path)
        if not nltk_kwic_df.empty:
            english_kwic['nltk'] = {
                'total_matches': len(nltk_kwic_df),
                'context_variety': len(nltk_kwic_df['Left_Context'].unique()) if 'Left_Context' in nltk_kwic_df.columns else 0
            }
            print(f"nltk KWIC: {len(nltk_kwic_df)}个匹配")
        
        # spacy
        spacy_kwic_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_KWIC_china.csv"
        spacy_kwic_df = self.load_csv_safely(spacy_kwic_path)
        if not spacy_kwic_df.empty:
            english_kwic['spacy'] = {
                'total_matches': len(spacy_kwic_df),
                'context_variety': len(spacy_kwic_df['Left_Context'].unique()) if 'Left_Context' in spacy_kwic_df.columns else 0
            }
            print(f"spacy KWIC: {len(spacy_kwic_df)}个匹配")
        
        # textblob
        textblob_kwic_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_KWIC_china.csv"
        textblob_kwic_df = self.load_csv_safely(textblob_kwic_path)
        if not textblob_kwic_df.empty:
            english_kwic['textblob'] = {
                'total_matches': len(textblob_kwic_df),
                'context_variety': len(textblob_kwic_df['Left_Context'].unique()) if 'Left_Context' in textblob_kwic_df.columns else 0
            }
            print(f"textblob KWIC: {len(textblob_kwic_df)}个匹配")
        
        results['英文'] = english_kwic
        
        # 德文KWIC分析
        print("\n【德文KWIC分析对比】")
        german_kwic = {}
        
        # nltk
        nltk_kwic_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_KWIC_china.csv"
        nltk_kwic_df = self.load_csv_safely(nltk_kwic_path)
        if not nltk_kwic_df.empty:
            german_kwic['nltk'] = {
                'total_matches': len(nltk_kwic_df),
                'context_variety': len(nltk_kwic_df['Left_Context'].unique()) if 'Left_Context' in nltk_kwic_df.columns else 0
            }
            print(f"nltk KWIC: {len(nltk_kwic_df)}个匹配")
        
        results['德文'] = german_kwic
        
        return results

    def analyze_all_features_comprehensive(self):
        """全面分析所有功能"""
        print("开始完整功能对比分析...")

        # 1. 词频分析
        word_freq_results = self.analyze_word_frequency_comprehensive()

        # 2. KWIC分析
        kwic_results = self.analyze_kwic_comprehensive()

        # 3. 其他功能统计
        print("\n" + "="*60)
        print("3. 其他功能统计对比")
        print("="*60)

        # 统计各语言各库的文件完整性
        feature_completeness = self.analyze_feature_completeness()

        return {
            'word_frequency': word_freq_results,
            'kwic': kwic_results,
            'feature_completeness': feature_completeness
        }

    def analyze_feature_completeness(self):
        """分析功能完整性"""
        completeness = {}

        # 定义所有应该存在的功能文件
        features = {
            '词频分析': 'word_frequency_report.csv',
            'KWIC分析': 'KWIC_',
            'Plot分析': 'plot_',
            '词簇分析': 'cluster_',
            '搭配分析': 'collocate_',
            '可视化报告': 'visualization_report.html'
        }

        languages = {
            '中文': ['jieba', 'nltk', 'spacy'],
            '英文': ['nltk', 'spacy', 'textblob'],
            '德文': ['nltk', 'spacy']
        }

        for language, libs in languages.items():
            completeness[language] = {}

            for lib in libs:
                if language == '中文':
                    lib_dir = self.base_dir / "中文分析" / f"{lib}_analysis"
                elif language == '英文':
                    lib_dir = self.base_dir / "英文分析" / f"{lib}_analysis"
                elif language == '德文':
                    lib_dir = self.base_dir / "德文分析" / f"{lib}_analysis"

                feature_count = 0
                total_features = len(features)

                if lib_dir.exists():
                    files = list(lib_dir.glob("*.csv")) + list(lib_dir.glob("*.html"))

                    for feature_name, pattern in features.items():
                        if any(pattern in str(f) for f in files):
                            feature_count += 1

                completeness_rate = feature_count / total_features
                completeness[language][lib] = {
                    'completed_features': feature_count,
                    'total_features': total_features,
                    'completeness_rate': completeness_rate
                }

                print(f"{language}-{lib}: {feature_count}/{total_features} 功能完成 ({completeness_rate:.1%})")

        return completeness

    def generate_comprehensive_report(self, all_results):
        """生成全面对比报告"""
        report = []
        report.append("# 多语言语料库分析完整功能对比报告\n")
        report.append("## 报告概述\n")
        report.append("本报告基于实际生成的分析文件，对三种语言（中文、英文、德文）使用不同Python库的")
        report.append("六大核心功能进行了全面深度对比分析，包括数据量、质量、完整性等多个维度。\n\n")

        # 1. 词频分析对比
        if 'word_frequency' in all_results:
            report.append("## 1. 词频分析功能对比\n")

            wf_results = all_results['word_frequency']

            for language, libs in wf_results.items():
                report.append(f"### {language}词频分析\n")

                if libs:
                    report.append("| 库名称 | 词汇总数 | 最高频率 | 平均频率 | 最低频率 | 前5高频词 |\n")
                    report.append("|--------|----------|----------|----------|----------|------------|\n")

                    for lib, stats in libs.items():
                        total_words = stats['total_words']
                        max_freq = stats['max_frequency']
                        mean_freq = f"{stats['mean_frequency']:.2f}" if stats['mean_frequency'] > 0 else "N/A"
                        min_freq = stats['min_frequency']
                        top_words = ', '.join(stats['top_10_words'][:5])  # 只显示前5个

                        report.append(f"| {lib} | {total_words} | {max_freq} | {mean_freq} | {min_freq} | {top_words} |\n")

                    report.append("\n")

        # 2. KWIC分析对比
        if 'kwic' in all_results:
            report.append("## 2. KWIC分析功能对比\n")

            kwic_results = all_results['kwic']

            for language, libs in kwic_results.items():
                report.append(f"### {language}KWIC分析\n")

                if libs:
                    report.append("| 库名称 | 匹配总数 | 上下文多样性 | 匹配效率 |\n")
                    report.append("|--------|----------|--------------|----------|\n")

                    for lib, stats in libs.items():
                        total_matches = stats['total_matches']
                        context_variety = stats['context_variety']
                        efficiency = "高" if total_matches > 100 else "中" if total_matches > 20 else "低"

                        report.append(f"| {lib} | {total_matches} | {context_variety} | {efficiency} |\n")

                    report.append("\n")

        # 3. 功能完整性对比
        if 'feature_completeness' in all_results:
            report.append("## 3. 功能完整性对比\n")

            completeness = all_results['feature_completeness']

            report.append("| 语言 | 库名称 | 完成功能数 | 总功能数 | 完整率 | 评级 |\n")
            report.append("|------|--------|------------|----------|--------|------|\n")

            for language, libs in completeness.items():
                for lib, stats in libs.items():
                    completed = stats['completed_features']
                    total = stats['total_features']
                    rate = stats['completeness_rate']

                    if rate >= 0.8:
                        grade = "优秀"
                    elif rate >= 0.6:
                        grade = "良好"
                    elif rate >= 0.4:
                        grade = "一般"
                    else:
                        grade = "待完善"

                    report.append(f"| {language} | {lib} | {completed} | {total} | {rate:.1%} | {grade} |\n")

            report.append("\n")

        # 4. 综合评估和推荐
        report.append("## 4. 综合评估与推荐\n")

        # 基于实际数据的推荐
        recommendations = self.generate_recommendations(all_results)

        report.append("### 最佳库推荐\n")
        report.append("| 语言 | 推荐库 | 推荐理由 | 适用场景 |\n")
        report.append("|------|--------|----------|----------|\n")

        for language, (best_lib, reason, scenario) in recommendations.items():
            report.append(f"| {language} | **{best_lib}** | {reason} | {scenario} |\n")

        report.append("\n### 详细分析结论\n")

        # 中文分析结论
        report.append("#### 中文分析结论\n")
        if '中文' in all_results.get('word_frequency', {}):
            chinese_libs = all_results['word_frequency']['中文']
            if 'jieba' in chinese_libs and 'spacy' in chinese_libs:
                jieba_words = chinese_libs['jieba']['total_words']
                spacy_words = chinese_libs['spacy']['total_words']

                report.append(f"- **词汇识别能力**: jieba识别了{jieba_words}个词汇，spaCy识别了{spacy_words}个词汇\n")
                report.append(f"- **处理效果**: {'jieba' if jieba_words > spacy_words else 'spacy'}在词汇识别数量上表现更好\n")

        report.append("- **专业性**: jieba专门针对中文优化，在中文分词方面具有明显优势\n")
        report.append("- **功能完整性**: 各库都实现了核心功能，jieba在中文处理上更加专业\n\n")

        # 英文分析结论
        report.append("#### 英文分析结论\n")
        if '英文' in all_results.get('word_frequency', {}):
            english_libs = all_results['word_frequency']['英文']
            lib_performance = []
            for lib, stats in english_libs.items():
                lib_performance.append((lib, stats['total_words']))

            if lib_performance:
                best_lib = max(lib_performance, key=lambda x: x[1])
                report.append(f"- **词汇识别**: {best_lib[0]}识别了最多的词汇({best_lib[1]}个)\n")

        report.append("- **成熟度**: NLTK作为经典库，在英文处理方面最为成熟稳定\n")
        report.append("- **现代化**: spaCy提供了更现代化的API和更好的性能\n")
        report.append("- **易用性**: TextBlob简单易用，适合快速原型开发\n\n")

        # 德文分析结论
        report.append("#### 德文分析结论\n")
        if '德文' in all_results.get('word_frequency', {}):
            german_libs = all_results['word_frequency']['德文']
            if 'nltk' in german_libs and 'spacy' in german_libs:
                nltk_words = german_libs['nltk']['total_words']
                spacy_words = german_libs['spacy']['total_words']

                report.append(f"- **词汇识别**: NLTK识别了{nltk_words}个词汇，spaCy识别了{spacy_words}个词汇\n")

        report.append("- **德文支持**: 两个库都对德文有良好支持\n")
        report.append("- **准确性**: 基于之前的AntConc对比，NLTK在德文处理上与标准结果更接近\n\n")

        # 技术建议
        report.append("### 技术选择建议\n")
        report.append("1. **中文项目**: 优先选择jieba，配合spaCy进行高级分析\n")
        report.append("2. **英文项目**: NLTK用于学术研究，spaCy用于生产环境\n")
        report.append("3. **德文项目**: NLTK作为首选，spaCy作为备选\n")
        report.append("4. **多语言项目**: 根据主要语言选择核心库，其他语言使用通用库\n")

        return ''.join(report)

    def generate_recommendations(self, all_results):
        """基于实际数据生成推荐"""
        recommendations = {}

        # 中文推荐
        if '中文' in all_results.get('word_frequency', {}):
            chinese_libs = all_results['word_frequency']['中文']
            if chinese_libs:
                # 基于词汇数量和完整性推荐
                best_lib = max(chinese_libs.keys(), key=lambda x: chinese_libs[x]['total_words'])
                recommendations['中文'] = (
                    best_lib,
                    f"词汇识别能力强({chinese_libs[best_lib]['total_words']}个词汇)，中文处理专业",
                    "中文文本分析、内容挖掘、学术研究"
                )

        # 英文推荐
        if '英文' in all_results.get('word_frequency', {}):
            english_libs = all_results['word_frequency']['英文']
            if english_libs:
                # 优先推荐NLTK（基于之前的AntConc对比结果）
                if 'nltk' in english_libs:
                    recommendations['英文'] = (
                        'nltk',
                        "成熟稳定，与AntConc结果相关性高，学术标准",
                        "英文学术研究、标准化分析、教学"
                    )
                else:
                    best_lib = max(english_libs.keys(), key=lambda x: english_libs[x]['total_words'])
                    recommendations['英文'] = (
                        best_lib,
                        f"词汇识别全面({english_libs[best_lib]['total_words']}个词汇)",
                        "英文文本分析、商业应用"
                    )

        # 德文推荐
        if '德文' in all_results.get('word_frequency', {}):
            german_libs = all_results['word_frequency']['德文']
            if german_libs:
                # 优先推荐NLTK（基于之前的AntConc完美匹配）
                if 'nltk' in german_libs:
                    recommendations['德文'] = (
                        'nltk',
                        "与AntConc结果完美匹配，德文处理准确",
                        "德文文本分析、政治文本研究"
                    )
                else:
                    best_lib = list(german_libs.keys())[0]
                    recommendations['德文'] = (
                        best_lib,
                        "德文处理能力良好",
                        "德文文本分析"
                    )

        return recommendations

def main():
    """主函数"""
    print("开始完整功能对比分析...")

    # 创建分析器
    comparator = CompleteFeatureComparator()

    # 运行全面分析
    all_results = comparator.analyze_all_features_comprehensive()

    # 生成报告
    report_text = comparator.generate_comprehensive_report(all_results)

    # 保存报告
    report_path = "完整功能对比分析报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_text)

    print(f"\n完整功能对比分析报告已保存到: {report_path}")

    # 显示推荐结果
    recommendations = comparator.generate_recommendations(all_results)
    print("\n" + "="*60)
    print("基于实际数据的最终推荐:")
    print("="*60)

    for language, (best_lib, reason, scenario) in recommendations.items():
        print(f"{language}: {best_lib}")
        print(f"  推荐理由: {reason}")
        print(f"  适用场景: {scenario}")
        print()

if __name__ == "__main__":
    main()
