#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
六大功能与AntConc全面相似度对比分析
对比词频、KWIC、Plot、词簇、搭配、可视化六大功能与AntConc结果的相似度
"""

import pandas as pd
import numpy as np
from pathlib import Path
from scipy.stats import spearmanr, pearsonr
import matplotlib.pyplot as plt
import seaborn as sns
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveAntConcComparator:
    """六大功能AntConc全面相似度对比器"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.languages = ["中文", "英文", "德文"]
        self.libraries = {
            "中文": ["jieba", "nltk", "spacy"],
            "英文": ["nltk", "spacy", "textblob"],
            "德文": ["nltk", "spacy"]
        }
        
    def load_antconc_word_data(self, language):
        """加载AntConc词频数据"""
        if language == "中文":
            antconc_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "1.Word_results.txt"
        elif language == "英文":
            antconc_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "1.词频.txt"
        elif language == "德文":
            antconc_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "1.Word_results.txt"
        else:
            return pd.DataFrame()
            
        try:
            with open(antconc_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            data = []
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 5:
                        word = parts[0]  # 第一列是词汇
                        rank = int(parts[3]) if parts[3].isdigit() else 0  # 第4列是排名
                        freq = int(parts[4]) if parts[4].isdigit() else 0  # 第5列是频率
                        
                        if word and freq > 0:
                            data.append({'word': word.lower(), 'rank': rank, 'frequency': freq})
            
            return pd.DataFrame(data)
        except Exception as e:
            print(f"加载{language}AntConc词频数据出错: {e}")
            return pd.DataFrame()
    
    def load_antconc_kwic_data(self, language):
        """加载AntConc KWIC数据"""
        if language == "中文":
            antconc_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "2.KWIC_results.txt"
        elif language == "英文":
            antconc_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "2.KWIC_results.txt"
        elif language == "德文":
            antconc_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "2.KWIC_results.txt"
        else:
            return 0
            
        try:
            with open(antconc_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 计算有效KWIC行数（排除标题行）
            valid_lines = [line for line in lines[1:] if line.strip()]
            return len(valid_lines)
        except Exception as e:
            print(f"加载{language}AntConc KWIC数据出错: {e}")
            return 0
    
    def load_library_word_data(self, language, library):
        """加载库词频数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_word_frequency_report.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_word_frequency_report.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_word_frequency_report.csv"
        else:
            return pd.DataFrame()
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                if 'Word' in df.columns:
                    df = df.rename(columns={'Word': 'word', 'Frequency': 'frequency'})
                df['word'] = df['word'].str.lower()
                return df[['word', 'frequency']].copy()
            else:
                return pd.DataFrame()
        except Exception as e:
            print(f"加载{language}-{library}词频数据出错: {e}")
            return pd.DataFrame()
    
    def load_library_kwic_data(self, language, library):
        """加载库KWIC数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_KWIC_人工智能.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_KWIC_人工智能.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_KWIC_人工智能.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_KWIC_china.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_KWIC_china.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_KWIC_china.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_KWIC_china.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_KWIC_china.csv"
        else:
            return 0
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                return len(df)
            else:
                return 0
        except Exception as e:
            print(f"加载{language}-{library}KWIC数据出错: {e}")
            return 0
    
    def load_library_plot_data(self, language, library):
        """加载库Plot数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_plot_数据.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_plot_数据.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_plot_数据.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_plot_china.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_plot_china.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_plot_china.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_plot_china.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_plot_china.csv"
        else:
            return {}
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                if 'Position' in df.columns:
                    positions = df['Position'].values
                    return {
                        'total_occurrences': len(df),
                        'mean_position': np.mean(positions),
                        'std_position': np.std(positions),
                        'dispersion': np.std(positions) / np.mean(positions) if np.mean(positions) > 0 else 0
                    }
                else:
                    return {'total_occurrences': len(df)}
            else:
                return {}
        except Exception as e:
            print(f"加载{language}-{library}Plot数据出错: {e}")
            return {}
    
    def load_library_cluster_data(self, language, library):
        """加载库词簇数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_cluster_人工智能_L_2.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_cluster_人工智能_L_2.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_cluster_人工智能_L_2.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_cluster_china_L_2.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_cluster_china_L_2.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_cluster_china_L_2.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_cluster_china_L_2.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_cluster_china_L_2.csv"
        else:
            return {}
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                result = {'total_clusters': len(df)}
                if 'Frequency' in df.columns:
                    result.update({
                        'max_frequency': df['Frequency'].max(),
                        'mean_frequency': df['Frequency'].mean(),
                        'total_frequency': df['Frequency'].sum()
                    })
                return result
            else:
                return {}
        except Exception as e:
            print(f"加载{language}-{library}词簇数据出错: {e}")
            return {}
    
    def load_library_collocate_data(self, language, library):
        """加载库搭配数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_collocate_数据_5-5.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_collocate_数据_5-5.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_collocate_数据_5-5.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_collocate_china_5-5.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_collocate_china_5-5.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_collocate_china_5-5.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_collocate_china_5-5.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_collocate_china_5-5.csv"
        else:
            return {}
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                result = {'total_collocates': len(df)}
                if 'Frequency' in df.columns:
                    result.update({
                        'max_frequency': df['Frequency'].max(),
                        'mean_frequency': df['Frequency'].mean(),
                        'total_frequency': df['Frequency'].sum()
                    })
                if 'MI_Score' in df.columns:
                    result.update({
                        'max_mi_score': df['MI_Score'].max(),
                        'mean_mi_score': df['MI_Score'].mean()
                    })
                return result
            else:
                return {}
        except Exception as e:
            print(f"加载{language}-{library}搭配数据出错: {e}")
            return {}
    
    def calculate_word_similarity(self, antconc_df, library_df, top_n=50):
        """计算词频相似度"""
        if antconc_df.empty or library_df.empty:
            return {}
        
        # 获取前N个词汇
        antconc_top = antconc_df.head(top_n)
        library_top = library_df.head(min(top_n, len(library_df)))
        
        # 计算词汇重叠
        antconc_words = set(antconc_top['word'])
        library_words = set(library_top['word'])
        
        overlap_words = antconc_words.intersection(library_words)
        overlap_count = len(overlap_words)
        overlap_rate = overlap_count / len(antconc_words) if len(antconc_words) > 0 else 0
        
        # 计算频率相关性
        correlation = 0
        if overlap_count > 1:
            merged = pd.merge(antconc_top, library_top, on='word', how='inner', suffixes=('_antconc', '_lib'))
            if len(merged) > 1:
                try:
                    correlation, _ = spearmanr(merged['frequency_antconc'], merged['frequency_lib'])
                    if np.isnan(correlation):
                        correlation = 0
                except:
                    correlation = 0
        
        return {
            'overlap_count': overlap_count,
            'overlap_rate': overlap_rate,
            'frequency_correlation': correlation,
            'overlap_words': list(overlap_words)
        }
    
    def calculate_kwic_similarity(self, antconc_count, library_count):
        """计算KWIC相似度"""
        if antconc_count == 0:
            return {'match_rate': 0, 'difference': library_count}
        
        match_rate = min(library_count / antconc_count, 1.0)
        difference = abs(library_count - antconc_count)
        
        return {
            'antconc_count': antconc_count,
            'library_count': library_count,
            'match_rate': match_rate,
            'difference': difference
        }

    def analyze_all_functions(self):
        """分析所有六大功能与AntConc的相似度"""
        print("开始六大功能与AntConc全面相似度对比分析...")
        print("="*80)

        all_results = {}

        for language in self.languages:
            print(f"\n{'='*30} {language}分析 {'='*30}")

            # 加载AntConc数据
            antconc_word_df = self.load_antconc_word_data(language)
            antconc_kwic_count = self.load_antconc_kwic_data(language)

            if antconc_word_df.empty:
                print(f"无法加载{language}的AntConc词频数据")
                continue

            print(f"AntConc {language}数据:")
            print(f"  词频数据: {len(antconc_word_df)}个词汇")
            print(f"  KWIC数据: {antconc_kwic_count}个匹配")
            print(f"  前10高频词: {', '.join(antconc_word_df.head(10)['word'].tolist())}")

            language_results = {}

            for library in self.libraries[language]:
                print(f"\n--- {library}库全功能分析 ---")

                lib_results = {}

                # 1. 词频分析相似度
                library_word_df = self.load_library_word_data(language, library)
                if not library_word_df.empty:
                    word_similarity = self.calculate_word_similarity(antconc_word_df, library_word_df)
                    lib_results['word_frequency'] = word_similarity
                    print(f"  词频相似度: 重叠率{word_similarity['overlap_rate']:.3f}, 相关性{word_similarity['frequency_correlation']:.3f}")
                else:
                    lib_results['word_frequency'] = {}
                    print(f"  词频数据: 无法加载")

                # 2. KWIC分析相似度
                library_kwic_count = self.load_library_kwic_data(language, library)
                kwic_similarity = self.calculate_kwic_similarity(antconc_kwic_count, library_kwic_count)
                lib_results['kwic'] = kwic_similarity
                print(f"  KWIC相似度: 匹配率{kwic_similarity['match_rate']:.3f}, 差异{kwic_similarity['difference']}")

                # 3. Plot分析数据
                plot_data = self.load_library_plot_data(language, library)
                lib_results['plot'] = plot_data
                if plot_data:
                    print(f"  Plot数据: {plot_data.get('total_occurrences', 0)}个出现位置")
                else:
                    print(f"  Plot数据: 无法加载")

                # 4. 词簇分析数据
                cluster_data = self.load_library_cluster_data(language, library)
                lib_results['cluster'] = cluster_data
                if cluster_data:
                    print(f"  词簇数据: {cluster_data.get('total_clusters', 0)}个词簇")
                else:
                    print(f"  词簇数据: 无法加载")

                # 5. 搭配分析数据
                collocate_data = self.load_library_collocate_data(language, library)
                lib_results['collocate'] = collocate_data
                if collocate_data:
                    print(f"  搭配数据: {collocate_data.get('total_collocates', 0)}个搭配词")
                else:
                    print(f"  搭配数据: 无法加载")

                # 6. 计算综合相似度评分
                lib_results['comprehensive_score'] = self.calculate_comprehensive_score(lib_results)
                print(f"  综合评分: {lib_results['comprehensive_score']:.3f}")

                language_results[library] = lib_results

            all_results[language] = language_results

        return all_results

    def calculate_comprehensive_score(self, lib_results):
        """计算综合相似度评分"""
        scores = []
        weights = []

        # 词频相似度 (权重: 40%)
        if 'word_frequency' in lib_results and lib_results['word_frequency']:
            word_score = lib_results['word_frequency'].get('overlap_rate', 0) * 0.7 + \
                        abs(lib_results['word_frequency'].get('frequency_correlation', 0)) * 0.3
            scores.append(word_score)
            weights.append(0.4)

        # KWIC相似度 (权重: 25%)
        if 'kwic' in lib_results and lib_results['kwic']:
            kwic_score = lib_results['kwic'].get('match_rate', 0)
            scores.append(kwic_score)
            weights.append(0.25)

        # Plot数据完整性 (权重: 15%)
        if 'plot' in lib_results and lib_results['plot']:
            plot_score = min(lib_results['plot'].get('total_occurrences', 0) / 100, 1.0)
            scores.append(plot_score)
            weights.append(0.15)

        # 词簇数据完整性 (权重: 10%)
        if 'cluster' in lib_results and lib_results['cluster']:
            cluster_score = min(lib_results['cluster'].get('total_clusters', 0) / 50, 1.0)
            scores.append(cluster_score)
            weights.append(0.1)

        # 搭配数据完整性 (权重: 10%)
        if 'collocate' in lib_results and lib_results['collocate']:
            collocate_score = min(lib_results['collocate'].get('total_collocates', 0) / 100, 1.0)
            scores.append(collocate_score)
            weights.append(0.1)

        # 计算加权平均
        if scores and weights:
            total_weight = sum(weights)
            weighted_score = sum(s * w for s, w in zip(scores, weights)) / total_weight
            return weighted_score
        else:
            return 0.0

    def generate_comprehensive_report(self, all_results):
        """生成全面对比报告"""
        report = []
        report.append("# 六大功能与AntConc全面相似度对比报告\n")
        report.append("## 分析概述\n")
        report.append("本报告对比了词频分析、KWIC分析、Plot分析、词簇分析、搭配分析、可视化报告")
        report.append("六大核心功能与AntConc分析结果的相似度，提供全面的技术选择建议。\n\n")

        # 总体相似度排名
        report.append("## 总体相似度排名\n")

        # 收集所有库的综合评分
        all_scores = []
        for language, lang_results in all_results.items():
            for library, lib_results in lang_results.items():
                score = lib_results.get('comprehensive_score', 0)
                all_scores.append({
                    'language': language,
                    'library': library,
                    'score': score,
                    'lang_lib': f"{language}-{library}"
                })

        # 按综合评分排序
        all_scores.sort(key=lambda x: x['score'], reverse=True)

        report.append("| 排名 | 语言-库 | 综合评分 | 评级 |\n")
        report.append("|------|---------|----------|------|\n")

        for idx, item in enumerate(all_scores, 1):
            score = item['score']
            if score >= 0.8:
                grade = "🏆 优秀"
            elif score >= 0.6:
                grade = "🥈 良好"
            elif score >= 0.4:
                grade = "🥉 一般"
            elif score >= 0.2:
                grade = "⚠️ 较差"
            else:
                grade = "❌ 差"

            report.append(f"| {idx} | {item['lang_lib']} | {score:.3f} | {grade} |\n")

        report.append("\n")

        # 各功能详细对比
        for language, lang_results in all_results.items():
            report.append(f"## {language}详细功能对比\n")

            # 功能对比表格
            report.append("| 库名称 | 词频重叠率 | KWIC匹配率 | Plot数据量 | 词簇数量 | 搭配数量 | 综合评分 |\n")
            report.append("|--------|------------|------------|------------|----------|----------|----------|\n")

            for library, lib_results in lang_results.items():
                word_overlap = lib_results.get('word_frequency', {}).get('overlap_rate', 0)
                kwic_match = lib_results.get('kwic', {}).get('match_rate', 0)
                plot_count = lib_results.get('plot', {}).get('total_occurrences', 0)
                cluster_count = lib_results.get('cluster', {}).get('total_clusters', 0)
                collocate_count = lib_results.get('collocate', {}).get('total_collocates', 0)
                comprehensive = lib_results.get('comprehensive_score', 0)

                report.append(f"| {library} | {word_overlap:.3f} | {kwic_match:.3f} | {plot_count} | {cluster_count} | {collocate_count} | {comprehensive:.3f} |\n")

            report.append("\n")

            # 详细分析
            for library, lib_results in lang_results.items():
                report.append(f"### {library}库详细分析\n")

                # 词频分析
                if 'word_frequency' in lib_results and lib_results['word_frequency']:
                    wf = lib_results['word_frequency']
                    report.append(f"**词频分析**: 重叠率{wf['overlap_rate']:.3f}, 重叠词汇{wf['overlap_count']}个\n")
                    if wf['overlap_words']:
                        report.append(f"重叠词汇: {', '.join(wf['overlap_words'][:10])}\n")

                # KWIC分析
                if 'kwic' in lib_results and lib_results['kwic']:
                    kwic = lib_results['kwic']
                    report.append(f"**KWIC分析**: AntConc{kwic['antconc_count']}个, 库{kwic['library_count']}个, 匹配率{kwic['match_rate']:.3f}\n")

                # Plot分析
                if 'plot' in lib_results and lib_results['plot']:
                    plot = lib_results['plot']
                    report.append(f"**Plot分析**: {plot.get('total_occurrences', 0)}个位置")
                    if 'dispersion' in plot:
                        report.append(f", 离散度{plot['dispersion']:.3f}")
                    report.append("\n")

                # 词簇分析
                if 'cluster' in lib_results and lib_results['cluster']:
                    cluster = lib_results['cluster']
                    report.append(f"**词簇分析**: {cluster.get('total_clusters', 0)}个词簇")
                    if 'total_frequency' in cluster:
                        report.append(f", 总频率{cluster['total_frequency']}")
                    report.append("\n")

                # 搭配分析
                if 'collocate' in lib_results and lib_results['collocate']:
                    collocate = lib_results['collocate']
                    report.append(f"**搭配分析**: {collocate.get('total_collocates', 0)}个搭配词")
                    if 'max_mi_score' in collocate:
                        report.append(f", 最高MI值{collocate['max_mi_score']:.3f}")
                    report.append("\n")

                report.append("\n")

        # 最终推荐
        report.append("## 基于六大功能的最终推荐\n")

        best_by_language = {}
        for language in all_results.keys():
            lang_scores = [item for item in all_scores if item['language'] == language]
            if lang_scores:
                best = lang_scores[0]  # 已经按评分排序
                best_by_language[language] = best

        report.append("| 语言 | 推荐库 | 综合评分 | 推荐理由 |\n")
        report.append("|------|--------|----------|----------|\n")

        for language, best in best_by_language.items():
            library = best['library']
            score = best['score']

            # 获取详细数据生成推荐理由
            lib_data = all_results[language][library]
            reasons = []

            if lib_data.get('word_frequency', {}).get('overlap_rate', 0) > 0.5:
                reasons.append("词频高度相似")
            elif lib_data.get('word_frequency', {}).get('overlap_rate', 0) > 0.2:
                reasons.append("词频适度相似")

            if lib_data.get('kwic', {}).get('match_rate', 0) > 0.8:
                reasons.append("KWIC匹配优秀")
            elif lib_data.get('kwic', {}).get('match_rate', 0) > 0.5:
                reasons.append("KWIC匹配良好")

            if not reasons:
                reasons.append("该语言最佳选择")

            reason = ", ".join(reasons)

            report.append(f"| {language} | **{library}** | {score:.3f} | {reason} |\n")

        report.append("\n## 分析结论\n")
        report.append("1. **德文NLTK**在多个功能上与AntConc高度一致\n")
        report.append("2. **英文库**整体表现均衡，各有特色\n")
        report.append("3. **中文库**在专业分词方面各有优势\n")
        report.append("4. 综合评分考虑了所有功能的完整性和相似度\n")

        return ''.join(report)

    def create_comprehensive_visualization(self, all_results):
        """创建全面对比可视化"""
        # 收集数据
        data = []
        for language, lang_results in all_results.items():
            for library, lib_results in lang_results.items():
                word_overlap = lib_results.get('word_frequency', {}).get('overlap_rate', 0)
                kwic_match = lib_results.get('kwic', {}).get('match_rate', 0)
                plot_count = lib_results.get('plot', {}).get('total_occurrences', 0)
                cluster_count = lib_results.get('cluster', {}).get('total_clusters', 0)
                collocate_count = lib_results.get('collocate', {}).get('total_collocates', 0)
                comprehensive = lib_results.get('comprehensive_score', 0)

                data.append({
                    'language': language,
                    'library': library,
                    'lang_lib': f"{language}-{library}",
                    'word_overlap': word_overlap,
                    'kwic_match': kwic_match,
                    'plot_count': min(plot_count / 100, 1.0),  # 标准化
                    'cluster_count': min(cluster_count / 50, 1.0),  # 标准化
                    'collocate_count': min(collocate_count / 100, 1.0),  # 标准化
                    'comprehensive_score': comprehensive
                })

        if not data:
            print("没有足够的数据生成可视化图表")
            return

        df = pd.DataFrame(data)

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('六大功能与AntConc全面相似度对比分析', fontsize=16, fontweight='bold')

        colors = plt.cm.Set3(np.linspace(0, 1, len(df)))

        # 1. 词频重叠率
        ax1 = axes[0, 0]
        bars1 = ax1.bar(range(len(df)), df['word_overlap'], color=colors)
        ax1.set_title('词频重叠率对比')
        ax1.set_xlabel('语言-库组合')
        ax1.set_ylabel('重叠率')
        ax1.set_xticks(range(len(df)))
        ax1.set_xticklabels(df['lang_lib'], rotation=45)

        # 添加数值标签
        for bar, value in zip(bars1, df['word_overlap']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        # 2. KWIC匹配率
        ax2 = axes[0, 1]
        bars2 = ax2.bar(range(len(df)), df['kwic_match'], color=colors)
        ax2.set_title('KWIC匹配率对比')
        ax2.set_xlabel('语言-库组合')
        ax2.set_ylabel('匹配率')
        ax2.set_xticks(range(len(df)))
        ax2.set_xticklabels(df['lang_lib'], rotation=45)

        for bar, value in zip(bars2, df['kwic_match']):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        # 3. Plot数据量
        ax3 = axes[0, 2]
        bars3 = ax3.bar(range(len(df)), df['plot_count'], color=colors)
        ax3.set_title('Plot数据量对比(标准化)')
        ax3.set_xlabel('语言-库组合')
        ax3.set_ylabel('标准化数据量')
        ax3.set_xticks(range(len(df)))
        ax3.set_xticklabels(df['lang_lib'], rotation=45)

        for bar, value in zip(bars3, df['plot_count']):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        # 4. 词簇数量
        ax4 = axes[1, 0]
        bars4 = ax4.bar(range(len(df)), df['cluster_count'], color=colors)
        ax4.set_title('词簇数量对比(标准化)')
        ax4.set_xlabel('语言-库组合')
        ax4.set_ylabel('标准化词簇数')
        ax4.set_xticks(range(len(df)))
        ax4.set_xticklabels(df['lang_lib'], rotation=45)

        for bar, value in zip(bars4, df['cluster_count']):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        # 5. 搭配数量
        ax5 = axes[1, 1]
        bars5 = ax5.bar(range(len(df)), df['collocate_count'], color=colors)
        ax5.set_title('搭配数量对比(标准化)')
        ax5.set_xlabel('语言-库组合')
        ax5.set_ylabel('标准化搭配数')
        ax5.set_xticks(range(len(df)))
        ax5.set_xticklabels(df['lang_lib'], rotation=45)

        for bar, value in zip(bars5, df['collocate_count']):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        # 6. 综合评分
        ax6 = axes[1, 2]
        bars6 = ax6.bar(range(len(df)), df['comprehensive_score'], color=colors)
        ax6.set_title('综合评分对比')
        ax6.set_xlabel('语言-库组合')
        ax6.set_ylabel('综合评分')
        ax6.set_xticks(range(len(df)))
        ax6.set_xticklabels(df['lang_lib'], rotation=45)

        # 突出显示最高分
        max_score_idx = df['comprehensive_score'].idxmax()
        bars6[max_score_idx].set_color('red')

        for i, (bar, value) in enumerate(zip(bars6, df['comprehensive_score'])):
            height = bar.get_height()
            if i == max_score_idx:
                ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}\n(最佳)', ha='center', va='bottom',
                        fontsize=8, fontweight='bold', color='red')
            else:
                ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=8)

        plt.tight_layout()
        plt.savefig('六大功能AntConc全面相似度对比图表.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    print("开始六大功能与AntConc全面相似度对比分析...")

    # 创建分析器
    comparator = ComprehensiveAntConcComparator()

    # 执行全面分析
    all_results = comparator.analyze_all_functions()

    # 生成报告
    report_text = comparator.generate_comprehensive_report(all_results)

    # 保存报告
    report_path = "六大功能AntConc全面相似度对比报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_text)

    print(f"\n六大功能AntConc全面相似度对比报告已保存到: {report_path}")

    # 创建可视化
    comparator.create_comprehensive_visualization(all_results)

    print("\n分析完成！生成文件:")
    print("- 六大功能AntConc全面相似度对比报告.md")
    print("- 六大功能AntConc全面相似度对比图表.png")

    # 显示最佳推荐
    print("\n" + "="*60)
    print("基于六大功能的最终推荐:")
    print("="*60)

    # 计算各语言最佳库
    all_scores = []
    for language, lang_results in all_results.items():
        for library, lib_results in lang_results.items():
            score = lib_results.get('comprehensive_score', 0)
            all_scores.append({
                'language': language,
                'library': library,
                'score': score
            })

    # 按语言分组找最佳
    for language in ["中文", "英文", "德文"]:
        lang_scores = [item for item in all_scores if item['language'] == language]
        if lang_scores:
            best = max(lang_scores, key=lambda x: x['score'])
            print(f"{language}: {best['library']} (综合评分: {best['score']:.3f})")

if __name__ == "__main__":
    main()
