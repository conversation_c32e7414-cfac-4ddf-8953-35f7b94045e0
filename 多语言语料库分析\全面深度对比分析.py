#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面深度对比分析 - 六大功能完整对比
对比各语言库与AntConc在所有功能上的相似度
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import spearmanr, pearsonr
from sklearn.metrics import mean_squared_error
import re
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveAnalysisComparator:
    """全面分析对比器 - 六大功能完整对比"""
    
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.results = {}
        
    def load_csv_file(self, file_path):
        """安全加载CSV文件"""
        try:
            if file_path.exists():
                return pd.read_csv(file_path, encoding='utf-8')
            else:
                print(f"文件不存在: {file_path}")
                return pd.DataFrame()
        except Exception as e:
            print(f"加载文件出错 {file_path}: {e}")
            return pd.DataFrame()
    
    def parse_antconc_word_file(self, file_path):
        """解析AntConc词频文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            data = []
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 4:
                        word = parts[2] if len(parts) > 2 else parts[0]
                        rank = int(parts[3]) if len(parts) > 3 and parts[3].isdigit() else 0
                        freq = int(parts[4]) if len(parts) > 4 and parts[4].isdigit() else 0
                        if word and freq > 0:
                            data.append({'word': word, 'rank': rank, 'frequency': freq})
            
            return pd.DataFrame(data)
        except Exception as e:
            print(f"解析AntConc文件出错 {file_path}: {e}")
            return pd.DataFrame()
    
    def compare_word_frequency(self, language):
        """对比词频分析结果"""
        print(f"\n{'='*20} {language}词频分析对比 {'='*20}")
        
        # 加载AntConc结果
        if language == "中文":
            antconc_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "1.Word_results.txt"
            libraries = ["jieba", "nltk", "spacy"]
            lib_paths = {
                "jieba": self.base_dir / "中文分析" / "jieba_analysis" / "jieba_word_frequency_report.csv",
                "nltk": self.base_dir / "中文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv", 
                "spacy": self.base_dir / "中文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
            }
        elif language == "英文":
            antconc_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "1.词频.txt"
            libraries = ["nltk", "spacy", "textblob"]
            lib_paths = {
                "nltk": self.base_dir / "英文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv",
                "spacy": self.base_dir / "英文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv",
                "textblob": self.base_dir / "英文分析" / "textblob_analysis" / "textblob_word_frequency_report.csv"
            }
        elif language == "德文":
            antconc_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "1.Word_results.txt"
            libraries = ["nltk", "spacy"]
            lib_paths = {
                "nltk": self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_word_frequency_report.csv",
                "spacy": self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_word_frequency_report.csv"
            }
        
        # 加载AntConc数据
        antconc_df = self.parse_antconc_word_file(antconc_path)
        if antconc_df.empty:
            print(f"无法加载{language}的AntConc词频数据")
            return {}
        
        print(f"AntConc词频数据: {len(antconc_df)}个词汇")
        
        comparison_results = {}
        
        for lib in libraries:
            lib_df = self.load_csv_file(lib_paths[lib])
            if lib_df.empty:
                continue
                
            # 标准化列名
            if 'Word' in lib_df.columns:
                lib_df = lib_df.rename(columns={'Word': 'word', 'Frequency': 'frequency'})
            
            print(f"\n{lib}库词频数据: {len(lib_df)}个词汇")
            
            # 进行多层次对比
            results = {}
            
            # 1. 全量对比
            results['full_comparison'] = self.calculate_word_similarity(antconc_df, lib_df, top_n=len(antconc_df))
            
            # 2. 不同规模对比
            for top_n in [10, 20, 50, 100, 200]:
                if len(antconc_df) >= top_n:
                    results[f'top_{top_n}'] = self.calculate_word_similarity(antconc_df, lib_df, top_n=top_n)
            
            comparison_results[lib] = results
            
            # 输出详细结果
            print(f"\n{lib}库详细对比结果:")
            print(f"  全量对比 - 重叠率: {results['full_comparison']['overlap_rate']:.3f}, 重叠数: {results['full_comparison']['overlap_count']}")
            if 'top_50' in results:
                print(f"  前50词 - 重叠率: {results['top_50']['overlap_rate']:.3f}, 相关性: {results['top_50']['correlation']:.3f}")
            if 'top_100' in results:
                print(f"  前100词 - 重叠率: {results['top_100']['overlap_rate']:.3f}, 相关性: {results['top_100']['correlation']:.3f}")
        
        return comparison_results
    
    def calculate_word_similarity(self, antconc_df, library_df, top_n=100):
        """计算词频相似度"""
        # 获取前N个词汇
        antconc_top = antconc_df.head(top_n)
        library_top = library_df.head(top_n)
        
        # 计算词汇重叠
        antconc_words = set(antconc_top['word'].str.lower())
        library_words = set(library_top['word'].str.lower())
        
        overlap = antconc_words.intersection(library_words)
        overlap_count = len(overlap)
        overlap_rate = overlap_count / len(antconc_words) if len(antconc_words) > 0 else 0
        
        # 计算频率相关性（对于重叠词汇）
        correlation = 0
        if overlap_count > 1:
            merged = pd.merge(antconc_top, library_top, on='word', how='inner', suffixes=('_antconc', '_lib'))
            if len(merged) > 1:
                correlation = merged['frequency_antconc'].corr(merged['frequency_lib'])
        
        return {
            'overlap_count': overlap_count,
            'overlap_rate': overlap_rate,
            'correlation': correlation,
            'overlap_words': list(overlap),
            'antconc_total': len(antconc_words),
            'library_total': len(library_words)
        }
    
    def compare_kwic_analysis(self, language):
        """对比KWIC分析结果"""
        print(f"\n{'='*20} {language}KWIC分析对比 {'='*20}")
        
        # 加载AntConc KWIC结果
        if language == "中文":
            antconc_kwic_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "2.KWIC_results.txt"
            libraries = ["jieba", "nltk", "spacy"]
            lib_kwic_paths = {
                "jieba": self.base_dir / "中文分析" / "jieba_analysis" / "jieba_KWIC_人工智能.csv",
                "nltk": self.base_dir / "中文分析" / "nltk_analysis" / "nltk_KWIC_人工智能.csv",
                "spacy": self.base_dir / "中文分析" / "spacy_analysis" / "spacy_KWIC_人工智能.csv"
            }
        elif language == "英文":
            antconc_kwic_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "2.KWIC_results.txt"
            libraries = ["nltk", "spacy", "textblob"]
            lib_kwic_paths = {
                "nltk": self.base_dir / "英文分析" / "nltk_analysis" / "nltk_KWIC_china.csv",
                "spacy": self.base_dir / "英文分析" / "spacy_analysis" / "spacy_KWIC_china.csv",
                "textblob": self.base_dir / "英文分析" / "textblob_analysis" / "textblob_KWIC_china.csv"
            }
        elif language == "德文":
            antconc_kwic_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "2.KWIC_results.txt"
            libraries = ["nltk", "spacy"]
            lib_kwic_paths = {
                "nltk": self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_KWIC_china.csv",
                "spacy": self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_KWIC_china.csv"
            }
        
        # 解析AntConc KWIC结果
        antconc_kwic_count = self.parse_antconc_kwic(antconc_kwic_path)
        print(f"AntConc KWIC匹配数: {antconc_kwic_count}")
        
        comparison_results = {}
        
        for lib in libraries:
            lib_kwic_df = self.load_csv_file(lib_kwic_paths[lib])
            if not lib_kwic_df.empty:
                lib_kwic_count = len(lib_kwic_df)
                print(f"{lib}库 KWIC匹配数: {lib_kwic_count}")
                
                # 计算匹配度
                if antconc_kwic_count > 0:
                    match_rate = min(lib_kwic_count / antconc_kwic_count, 1.0)
                else:
                    match_rate = 0
                
                comparison_results[lib] = {
                    'antconc_count': antconc_kwic_count,
                    'library_count': lib_kwic_count,
                    'match_rate': match_rate,
                    'difference': abs(lib_kwic_count - antconc_kwic_count)
                }
                
                print(f"  匹配率: {match_rate:.3f}, 差异: {abs(lib_kwic_count - antconc_kwic_count)}")
        
        return comparison_results
    
    def parse_antconc_kwic(self, file_path):
        """解析AntConc KWIC文件，返回匹配数量"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 计算有效行数（排除标题行）
            valid_lines = [line for line in lines[1:] if line.strip()]
            return len(valid_lines)
        except Exception as e:
            print(f"解析AntConc KWIC文件出错: {e}")
            return 0
    
    def compare_plot_analysis(self, language):
        """对比Plot分析结果"""
        print(f"\n{'='*20} {language}Plot分析对比 {'='*20}")
        
        # 这里可以对比Plot分析的离散度、分布等指标
        # 由于AntConc的Plot结果格式复杂，我们主要对比统计指标
        
        if language == "中文":
            libraries = ["jieba", "nltk", "spacy"]
            lib_plot_paths = {
                "jieba": self.base_dir / "中文分析" / "jieba_analysis" / "jieba_plot_数据.csv",
                "nltk": self.base_dir / "中文分析" / "nltk_analysis" / "nltk_plot_数据.csv",
                "spacy": self.base_dir / "中文分析" / "spacy_analysis" / "spacy_plot_数据.csv"
            }
        elif language == "英文":
            libraries = ["nltk", "spacy", "textblob"]
            lib_plot_paths = {
                "nltk": self.base_dir / "英文分析" / "nltk_analysis" / "nltk_plot_china.csv",
                "spacy": self.base_dir / "英文分析" / "spacy_analysis" / "spacy_plot_china.csv",
                "textblob": self.base_dir / "英文分析" / "textblob_analysis" / "textblob_plot_china.csv"
            }
        elif language == "德文":
            libraries = ["nltk", "spacy"]
            lib_plot_paths = {
                "nltk": self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_plot_china.csv",
                "spacy": self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_plot_china.csv"
            }
        
        comparison_results = {}
        
        for lib in libraries:
            plot_df = self.load_csv_file(lib_plot_paths[lib])
            if not plot_df.empty:
                # 计算Plot统计指标
                if 'Position' in plot_df.columns:
                    positions = plot_df['Position'].values
                    dispersion = np.std(positions) / np.mean(positions) if np.mean(positions) > 0 else 0
                    
                    comparison_results[lib] = {
                        'total_occurrences': len(plot_df),
                        'dispersion_coefficient': dispersion,
                        'mean_position': np.mean(positions),
                        'std_position': np.std(positions)
                    }
                    
                    print(f"{lib}库 Plot分析:")
                    print(f"  总出现次数: {len(plot_df)}")
                    print(f"  离散系数: {dispersion:.4f}")
                    print(f"  平均位置: {np.mean(positions):.2f}")
        
        return comparison_results

    def compare_cluster_analysis(self, language):
        """对比词簇分析结果"""
        print(f"\n{'='*20} {language}词簇分析对比 {'='*20}")

        if language == "中文":
            libraries = ["jieba", "nltk", "spacy"]
            lib_cluster_paths = {
                "jieba": self.base_dir / "中文分析" / "jieba_analysis" / "jieba_cluster_人工智能_L_2.csv",
                "nltk": self.base_dir / "中文分析" / "nltk_analysis" / "nltk_cluster_人工智能_L_2.csv",
                "spacy": self.base_dir / "中文分析" / "spacy_analysis" / "spacy_cluster_人工智能_L_2.csv"
            }
        elif language == "英文":
            libraries = ["nltk", "spacy", "textblob"]
            lib_cluster_paths = {
                "nltk": self.base_dir / "英文分析" / "nltk_analysis" / "nltk_cluster_china_L_2.csv",
                "spacy": self.base_dir / "英文分析" / "spacy_analysis" / "spacy_cluster_china_L_2.csv",
                "textblob": self.base_dir / "英文分析" / "textblob_analysis" / "textblob_cluster_china_L_2.csv"
            }
        elif language == "德文":
            libraries = ["nltk", "spacy"]
            lib_cluster_paths = {
                "nltk": self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_cluster_china_L_2.csv",
                "spacy": self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_cluster_china_L_2.csv"
            }

        comparison_results = {}

        for lib in libraries:
            cluster_df = self.load_csv_file(lib_cluster_paths[lib])
            if not cluster_df.empty:
                # 分析词簇特征
                total_clusters = len(cluster_df)

                if 'Frequency' in cluster_df.columns:
                    freq_stats = {
                        'total_clusters': total_clusters,
                        'max_frequency': cluster_df['Frequency'].max(),
                        'mean_frequency': cluster_df['Frequency'].mean(),
                        'min_frequency': cluster_df['Frequency'].min(),
                        'frequency_std': cluster_df['Frequency'].std()
                    }
                else:
                    freq_stats = {'total_clusters': total_clusters}

                comparison_results[lib] = freq_stats

                print(f"{lib}库 词簇分析:")
                print(f"  词簇总数: {total_clusters}")
                if 'max_frequency' in freq_stats:
                    print(f"  最高频率: {freq_stats['max_frequency']}")
                    print(f"  平均频率: {freq_stats['mean_frequency']:.2f}")

        return comparison_results

    def compare_collocate_analysis(self, language):
        """对比搭配分析结果"""
        print(f"\n{'='*20} {language}搭配分析对比 {'='*20}")

        if language == "中文":
            libraries = ["jieba", "nltk", "spacy"]
            lib_collocate_paths = {
                "jieba": self.base_dir / "中文分析" / "jieba_analysis" / "jieba_collocate_数据_5-5.csv",
                "nltk": self.base_dir / "中文分析" / "nltk_analysis" / "nltk_collocate_数据_5-5.csv",
                "spacy": self.base_dir / "中文分析" / "spacy_analysis" / "spacy_collocate_数据_5-5.csv"
            }
        elif language == "英文":
            libraries = ["nltk", "spacy", "textblob"]
            lib_collocate_paths = {
                "nltk": self.base_dir / "英文分析" / "nltk_analysis" / "nltk_collocate_china_5-5.csv",
                "spacy": self.base_dir / "英文分析" / "spacy_analysis" / "spacy_collocate_china_5-5.csv",
                "textblob": self.base_dir / "英文分析" / "textblob_analysis" / "textblob_collocate_china_5-5.csv"
            }
        elif language == "德文":
            libraries = ["nltk", "spacy"]
            lib_collocate_paths = {
                "nltk": self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_collocate_china_5-5.csv",
                "spacy": self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_collocate_china_5-5.csv"
            }

        comparison_results = {}

        for lib in libraries:
            collocate_df = self.load_csv_file(lib_collocate_paths[lib])
            if not collocate_df.empty:
                # 分析搭配特征
                total_collocates = len(collocate_df)

                stats = {'total_collocates': total_collocates}

                if 'Frequency' in collocate_df.columns:
                    stats.update({
                        'max_frequency': collocate_df['Frequency'].max(),
                        'mean_frequency': collocate_df['Frequency'].mean(),
                        'frequency_std': collocate_df['Frequency'].std()
                    })

                if 'MI_Score' in collocate_df.columns:
                    stats.update({
                        'max_mi_score': collocate_df['MI_Score'].max(),
                        'mean_mi_score': collocate_df['MI_Score'].mean()
                    })

                comparison_results[lib] = stats

                print(f"{lib}库 搭配分析:")
                print(f"  搭配词总数: {total_collocates}")
                if 'max_frequency' in stats:
                    print(f"  最高频率: {stats['max_frequency']}")
                    print(f"  平均频率: {stats['mean_frequency']:.2f}")
                if 'max_mi_score' in stats:
                    print(f"  最高MI值: {stats['max_mi_score']:.4f}")

        return comparison_results

    def run_comprehensive_analysis(self):
        """运行全面对比分析"""
        print("开始全面深度对比分析...")
        print("="*60)

        all_results = {}

        # 分析三种语言
        for language in ["中文", "英文", "德文"]:
            print(f"\n{'#'*30} {language}分析 {'#'*30}")

            lang_results = {}

            # 1. 词频分析对比
            try:
                lang_results['word_frequency'] = self.compare_word_frequency(language)
            except Exception as e:
                print(f"词频分析对比出错: {e}")
                lang_results['word_frequency'] = {}

            # 2. KWIC分析对比
            try:
                lang_results['kwic'] = self.compare_kwic_analysis(language)
            except Exception as e:
                print(f"KWIC分析对比出错: {e}")
                lang_results['kwic'] = {}

            # 3. Plot分析对比
            try:
                lang_results['plot'] = self.compare_plot_analysis(language)
            except Exception as e:
                print(f"Plot分析对比出错: {e}")
                lang_results['plot'] = {}

            # 4. 词簇分析对比
            try:
                lang_results['cluster'] = self.compare_cluster_analysis(language)
            except Exception as e:
                print(f"词簇分析对比出错: {e}")
                lang_results['cluster'] = {}

            # 5. 搭配分析对比
            try:
                lang_results['collocate'] = self.compare_collocate_analysis(language)
            except Exception as e:
                print(f"搭配分析对比出错: {e}")
                lang_results['collocate'] = {}

            all_results[language] = lang_results

        return all_results

    def generate_comprehensive_report(self, all_results):
        """生成全面对比报告"""
        report = []
        report.append("# 多语言语料库分析全面深度对比报告\n")
        report.append("## 执行摘要\n")
        report.append("本报告对三种语言（中文、英文、德文）的六大分析功能进行了全面深度对比，")
        report.append("包括词频分析、KWIC分析、Plot分析、词簇分析、搭配分析等，")
        report.append("提供了详细的数据对比和技术选择建议。\n\n")

        # 生成总体对比表格
        report.append("## 总体对比概览\n")
        report.append("### 各语言库综合表现评分\n")
        report.append("| 语言 | 库名称 | 词频表现 | KWIC表现 | Plot表现 | 词簇表现 | 搭配表现 | 综合评分 |\n")
        report.append("|------|--------|----------|----------|----------|----------|----------|----------|\n")

        # 计算综合评分
        for language, lang_results in all_results.items():
            if 'word_frequency' in lang_results:
                for lib in lang_results['word_frequency'].keys():
                    # 计算各项评分
                    word_score = self.calculate_word_frequency_score(lang_results['word_frequency'].get(lib, {}))
                    kwic_score = self.calculate_kwic_score(lang_results['kwic'].get(lib, {}))
                    plot_score = self.calculate_plot_score(lang_results['plot'].get(lib, {}))
                    cluster_score = self.calculate_cluster_score(lang_results['cluster'].get(lib, {}))
                    collocate_score = self.calculate_collocate_score(lang_results['collocate'].get(lib, {}))

                    # 综合评分（加权平均）
                    total_score = (word_score * 0.3 + kwic_score * 0.2 + plot_score * 0.15 +
                                 cluster_score * 0.15 + collocate_score * 0.2)

                    report.append(f"| {language} | {lib} | {word_score:.2f} | {kwic_score:.2f} | {plot_score:.2f} | {cluster_score:.2f} | {collocate_score:.2f} | {total_score:.2f} |\n")

        report.append("\n")

        # 详细分析每种语言
        for language, lang_results in all_results.items():
            report.append(f"## {language}分析全面对比\n")

            # 词频分析对比
            if 'word_frequency' in lang_results and lang_results['word_frequency']:
                report.append("### 1. 词频分析对比\n")
                wf_results = lang_results['word_frequency']

                # 创建对比表格
                report.append("| 库名称 | 全量重叠率 | 前50词重叠率 | 前100词重叠率 | 前50词相关性 | 总词汇数 |\n")
                report.append("|--------|------------|--------------|---------------|--------------|----------|\n")

                for lib, results in wf_results.items():
                    full_rate = results['full_comparison']['overlap_rate']
                    top50_rate = results.get('top_50', {}).get('overlap_rate', 0)
                    top100_rate = results.get('top_100', {}).get('overlap_rate', 0)
                    top50_corr = results.get('top_50', {}).get('correlation', 0)
                    total_words = results['full_comparison']['library_total']

                    report.append(f"| {lib} | {full_rate:.3f} | {top50_rate:.3f} | {top100_rate:.3f} | {top50_corr:.3f} | {total_words} |\n")

                report.append("\n")

            # KWIC分析对比
            if 'kwic' in lang_results and lang_results['kwic']:
                report.append("### 2. KWIC分析对比\n")
                kwic_results = lang_results['kwic']

                report.append("| 库名称 | AntConc匹配数 | 库匹配数 | 匹配率 | 差异值 |\n")
                report.append("|--------|---------------|----------|--------|--------|\n")

                for lib, results in kwic_results.items():
                    antconc_count = results['antconc_count']
                    lib_count = results['library_count']
                    match_rate = results['match_rate']
                    difference = results['difference']

                    report.append(f"| {lib} | {antconc_count} | {lib_count} | {match_rate:.3f} | {difference} |\n")

                report.append("\n")

            # Plot分析对比
            if 'plot' in lang_results and lang_results['plot']:
                report.append("### 3. Plot分析对比\n")
                plot_results = lang_results['plot']

                report.append("| 库名称 | 总出现次数 | 离散系数 | 平均位置 | 标准差 |\n")
                report.append("|--------|------------|----------|----------|--------|\n")

                for lib, results in plot_results.items():
                    total_occ = results['total_occurrences']
                    dispersion = results['dispersion_coefficient']
                    mean_pos = results['mean_position']
                    std_pos = results['std_position']

                    report.append(f"| {lib} | {total_occ} | {dispersion:.4f} | {mean_pos:.2f} | {std_pos:.2f} |\n")

                report.append("\n")

            # 词簇分析对比
            if 'cluster' in lang_results and lang_results['cluster']:
                report.append("### 4. 词簇分析对比\n")
                cluster_results = lang_results['cluster']

                report.append("| 库名称 | 词簇总数 | 最高频率 | 平均频率 | 频率标准差 |\n")
                report.append("|--------|----------|----------|----------|------------|\n")

                for lib, results in cluster_results.items():
                    total_clusters = results['total_clusters']
                    max_freq = results.get('max_frequency', 'N/A')
                    mean_freq = results.get('mean_frequency', 'N/A')
                    std_freq = results.get('frequency_std', 'N/A')

                    if isinstance(mean_freq, float):
                        mean_freq = f"{mean_freq:.2f}"
                    if isinstance(std_freq, float):
                        std_freq = f"{std_freq:.2f}"

                    report.append(f"| {lib} | {total_clusters} | {max_freq} | {mean_freq} | {std_freq} |\n")

                report.append("\n")

            # 搭配分析对比
            if 'collocate' in lang_results and lang_results['collocate']:
                report.append("### 5. 搭配分析对比\n")
                collocate_results = lang_results['collocate']

                report.append("| 库名称 | 搭配词总数 | 最高频率 | 平均频率 | 最高MI值 | 平均MI值 |\n")
                report.append("|--------|------------|----------|----------|----------|----------|\n")

                for lib, results in collocate_results.items():
                    total_collocates = results['total_collocates']
                    max_freq = results.get('max_frequency', 'N/A')
                    mean_freq = results.get('mean_frequency', 'N/A')
                    max_mi = results.get('max_mi_score', 'N/A')
                    mean_mi = results.get('mean_mi_score', 'N/A')

                    if isinstance(mean_freq, float):
                        mean_freq = f"{mean_freq:.2f}"
                    if isinstance(max_mi, float):
                        max_mi = f"{max_mi:.4f}"
                    if isinstance(mean_mi, float):
                        mean_mi = f"{mean_mi:.4f}"

                    report.append(f"| {lib} | {total_collocates} | {max_freq} | {mean_freq} | {max_mi} | {mean_mi} |\n")

                report.append("\n")

        # 添加最终推荐
        report.append("## 最终推荐与结论\n")
        report.append("### 基于全面对比的库选择建议\n")

        # 计算每种语言的最佳库
        best_libs = self.calculate_best_libraries(all_results)

        report.append("| 语言 | 推荐库 | 推荐理由 | 综合评分 |\n")
        report.append("|------|--------|----------|----------|\n")

        for language, (best_lib, score, reason) in best_libs.items():
            report.append(f"| {language} | **{best_lib}** | {reason} | {score:.2f} |\n")

        report.append("\n### 技术选择指导原则\n")
        report.append("1. **词频分析**: 重点关注重叠率和相关性\n")
        report.append("2. **KWIC分析**: 重点关注匹配数量和准确性\n")
        report.append("3. **Plot分析**: 重点关注分布特征和离散度\n")
        report.append("4. **词簇分析**: 重点关注词簇数量和频率分布\n")
        report.append("5. **搭配分析**: 重点关注搭配强度和MI值\n")

        return ''.join(report)

    def calculate_word_frequency_score(self, results):
        """计算词频分析评分"""
        if not results:
            return 0

        # 基于重叠率和相关性计算评分
        full_overlap = results.get('full_comparison', {}).get('overlap_rate', 0)
        top50_overlap = results.get('top_50', {}).get('overlap_rate', 0)
        top50_corr = results.get('top_50', {}).get('correlation', 0)

        # 加权评分 (0-10分)
        score = (full_overlap * 2 + top50_overlap * 4 + abs(top50_corr) * 4) * 10
        return min(score, 10)

    def calculate_kwic_score(self, results):
        """计算KWIC分析评分"""
        if not results:
            return 0

        match_rate = results.get('match_rate', 0)
        # 基于匹配率计算评分 (0-10分)
        return match_rate * 10

    def calculate_plot_score(self, results):
        """计算Plot分析评分"""
        if not results:
            return 0

        # 基于出现次数和离散度计算评分
        total_occ = results.get('total_occurrences', 0)
        dispersion = results.get('dispersion_coefficient', 0)

        # 标准化评分 (0-10分)
        occ_score = min(total_occ / 100, 1) * 5  # 出现次数评分
        disp_score = min(dispersion * 2, 1) * 5  # 离散度评分

        return occ_score + disp_score

    def calculate_cluster_score(self, results):
        """计算词簇分析评分"""
        if not results:
            return 0

        total_clusters = results.get('total_clusters', 0)
        max_freq = results.get('max_frequency', 0)

        # 基于词簇数量和频率计算评分 (0-10分)
        cluster_score = min(total_clusters / 50, 1) * 5
        freq_score = min(max_freq / 100, 1) * 5

        return cluster_score + freq_score

    def calculate_collocate_score(self, results):
        """计算搭配分析评分"""
        if not results:
            return 0

        total_collocates = results.get('total_collocates', 0)
        max_mi = results.get('max_mi_score', 0)

        # 基于搭配数量和MI值计算评分 (0-10分)
        collocate_score = min(total_collocates / 100, 1) * 5
        mi_score = min(max_mi / 10, 1) * 5

        return collocate_score + mi_score

    def calculate_best_libraries(self, all_results):
        """计算每种语言的最佳库"""
        best_libs = {}

        for language, lang_results in all_results.items():
            if 'word_frequency' not in lang_results:
                continue

            lib_scores = {}

            for lib in lang_results['word_frequency'].keys():
                # 计算各项评分
                word_score = self.calculate_word_frequency_score(lang_results['word_frequency'].get(lib, {}))
                kwic_score = self.calculate_kwic_score(lang_results['kwic'].get(lib, {}))
                plot_score = self.calculate_plot_score(lang_results['plot'].get(lib, {}))
                cluster_score = self.calculate_cluster_score(lang_results['cluster'].get(lib, {}))
                collocate_score = self.calculate_collocate_score(lang_results['collocate'].get(lib, {}))

                # 综合评分（加权平均）
                total_score = (word_score * 0.3 + kwic_score * 0.2 + plot_score * 0.15 +
                             cluster_score * 0.15 + collocate_score * 0.2)

                lib_scores[lib] = total_score

            # 找出最佳库
            if lib_scores:
                best_lib = max(lib_scores, key=lib_scores.get)
                best_score = lib_scores[best_lib]

                # 生成推荐理由
                if language == "中文":
                    if best_lib == "jieba":
                        reason = "专业中文分词，综合表现最佳"
                    elif best_lib == "spacy":
                        reason = "功能丰富，现代化NLP库"
                    else:
                        reason = "学术研究标准库"
                elif language == "英文":
                    if best_lib == "nltk":
                        reason = "成熟稳定，与AntConc相似度高"
                    elif best_lib == "spacy":
                        reason = "现代化设计，处理效率高"
                    else:
                        reason = "简单易用，快速开发"
                elif language == "德文":
                    if best_lib == "nltk":
                        reason = "与AntConc结果高度一致"
                    else:
                        reason = "功能强大，德文支持良好"
                else:
                    reason = "综合评分最高"

                best_libs[language] = (best_lib, best_score, reason)

        return best_libs

def main():
    """主函数"""
    print("开始全面深度对比分析...")

    # 创建对比器
    comparator = ComprehensiveAnalysisComparator()

    # 运行全面分析
    all_results = comparator.run_comprehensive_analysis()

    # 生成详细报告
    report_text = comparator.generate_comprehensive_report(all_results)

    # 保存报告
    report_path = "全面深度对比分析报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_text)

    print(f"\n全面深度对比分析报告已保存到: {report_path}")

    # 打印简要结果
    print("\n" + "="*60)
    print("全面深度对比分析完成！")
    print("="*60)

    # 显示最佳推荐
    best_libs = comparator.calculate_best_libraries(all_results)
    print("\n最终推荐:")
    for language, (best_lib, score, reason) in best_libs.items():
        print(f"  {language}: {best_lib} (评分: {score:.2f}) - {reason}")

if __name__ == "__main__":
    main()
