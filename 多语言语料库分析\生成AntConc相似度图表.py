#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成AntConc相似度对比可视化图表
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_similarity_comparison():
    """创建相似度对比图表"""
    # 相似度数据
    data = {
        '语言-库': ['德文-NLTK', '英文-spaCy', '英文-NLTK', '英文-TextBlob', '中文-jieba', '中文-NLTK', '中文-spaCy'],
        '重叠率': [1.000, 0.320, 0.300, 0.300, 0.100, 0.060, 0.060],
        '频率相关性': [1.000, 1.000, 0.978, 0.986, 0.900, 1.000, 1.000],  # 取绝对值
        '排名相关性': [1.000, 0.962, 0.936, 0.943, 0.900, 1.000, 1.000],  # 取绝对值
        '重叠词汇数': [10, 16, 15, 15, 5, 3, 3],
        '语言': ['德文', '英文', '英文', '英文', '中文', '中文', '中文'],
        '库': ['NLTK', 'spaCy', 'NLTK', 'TextBlob', 'jieba', 'NLTK', 'spaCy']
    }
    
    df = pd.DataFrame(data)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('AntConc相似度专项对比分析', fontsize=16, fontweight='bold')
    
    # 1. 重叠率对比 - 突出德文NLTK
    ax1 = axes[0, 0]
    colors = ['#FF0000' if x == '德文-NLTK' else '#4ECDC4' if 'English' in x else '#45B7D1' 
              for x in df['语言-库']]
    colors = ['#FF0000', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    
    bars1 = ax1.bar(range(len(df)), df['重叠率'], color=colors)
    ax1.set_title('词汇重叠率对比')
    ax1.set_xlabel('语言-库组合')
    ax1.set_ylabel('重叠率')
    ax1.set_xticks(range(len(df)))
    ax1.set_xticklabels(df['语言-库'], rotation=45)
    ax1.set_ylim(0, 1.1)
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars1, df['重叠率'])):
        height = bar.get_height()
        if i == 0:  # 德文NLTK
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{value:.3f}\n(完美匹配)', ha='center', va='bottom', 
                    fontsize=10, fontweight='bold', color='red')
        else:
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 添加基准线
    ax1.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='完美匹配线')
    ax1.axhline(y=0.5, color='orange', linestyle='--', alpha=0.5, label='良好匹配线')
    ax1.legend()
    
    # 2. 频率相关性对比
    ax2 = axes[0, 1]
    bars2 = ax2.bar(range(len(df)), df['频率相关性'], color=colors)
    ax2.set_title('频率相关性对比')
    ax2.set_xlabel('语言-库组合')
    ax2.set_ylabel('频率相关性')
    ax2.set_xticks(range(len(df)))
    ax2.set_xticklabels(df['语言-库'], rotation=45)
    ax2.set_ylim(0, 1.1)
    
    for i, (bar, value) in enumerate(zip(bars2, df['频率相关性'])):
        height = bar.get_height()
        if i == 0:  # 德文NLTK
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', 
                    fontsize=10, fontweight='bold', color='red')
        else:
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    
    # 3. 重叠词汇数对比
    ax3 = axes[1, 0]
    bars3 = ax3.bar(range(len(df)), df['重叠词汇数'], color=colors)
    ax3.set_title('重叠词汇数对比')
    ax3.set_xlabel('语言-库组合')
    ax3.set_ylabel('重叠词汇数')
    ax3.set_xticks(range(len(df)))
    ax3.set_xticklabels(df['语言-库'], rotation=45)
    
    for i, (bar, value) in enumerate(zip(bars3, df['重叠词汇数'])):
        height = bar.get_height()
        if i == 0:  # 德文NLTK
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(value)}\n(10/10)', ha='center', va='bottom', 
                    fontsize=10, fontweight='bold', color='red')
        else:
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(value)}', ha='center', va='bottom', fontsize=9)
    
    # 4. 综合相似度雷达图
    ax4 = axes[1, 1]
    
    # 选择代表性的库进行雷达图展示
    selected_data = df[df['语言-库'].isin(['德文-NLTK', '英文-spaCy', '中文-jieba'])]
    
    categories = ['重叠率', '频率相关性', '排名相关性']
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]
    
    ax4 = plt.subplot(2, 2, 4, projection='polar')
    
    colors_radar = ['#FF0000', '#4ECDC4', '#FFEAA7']
    
    for idx, (_, row) in enumerate(selected_data.iterrows()):
        values = [row['重叠率'], row['频率相关性'], row['排名相关性']]
        values += values[:1]
        
        label = row['语言-库']
        if label == '德文-NLTK':
            label += ' (完美匹配)'
            
        ax4.plot(angles, values, 'o-', linewidth=2, label=label, color=colors_radar[idx])
        ax4.fill(angles, values, alpha=0.25, color=colors_radar[idx])
    
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(categories)
    ax4.set_ylim(0, 1)
    ax4.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax4.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    ax4.grid(True)
    ax4.set_title('推荐库综合相似度对比', pad=20)
    ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('AntConc相似度专项对比图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_language_comparison():
    """创建按语言分组的对比图表"""
    # 数据
    languages = ['德文', '英文', '中文']
    nltk_scores = [1.000, 0.300, 0.060]
    spacy_scores = [None, 0.320, 0.060]  # 德文spaCy数据缺失
    other_scores = [None, 0.300, 0.100]  # TextBlob for English, jieba for Chinese
    
    x = np.arange(len(languages))
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 创建柱状图
    bars1 = ax.bar(x - width, nltk_scores, width, label='NLTK', color='#FF6B6B', alpha=0.8)
    bars2 = ax.bar(x, [s if s is not None else 0 for s in spacy_scores], width, label='spaCy', color='#4ECDC4', alpha=0.8)
    bars3 = ax.bar(x + width, [s if s is not None else 0 for s in other_scores], width, label='其他库', color='#45B7D1', alpha=0.8)
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars1, nltk_scores)):
        if value is not None:
            height = bar.get_height()
            if i == 0:  # 德文
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                       f'{value:.3f}\n(完美)', ha='center', va='bottom', 
                       fontsize=11, fontweight='bold', color='red')
            else:
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                       f'{value:.3f}', ha='center', va='bottom', fontsize=10)
    
    for i, (bar, value) in enumerate(zip(bars2, spacy_scores)):
        if value is not None:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=10)
    
    for i, (bar, value) in enumerate(zip(bars3, other_scores)):
        if value is not None:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=10)
    
    ax.set_xlabel('语言')
    ax.set_ylabel('与AntConc重叠率')
    ax.set_title('各语言库与AntConc相似度对比')
    ax.set_xticks(x)
    ax.set_xticklabels(languages)
    ax.legend()
    ax.set_ylim(0, 1.1)
    
    # 添加基准线
    ax.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='完美匹配')
    ax.axhline(y=0.5, color='orange', linestyle='--', alpha=0.5, label='良好匹配')
    
    # 添加文本说明
    ax.text(0, 1.05, '德文NLTK\n完美匹配!', ha='center', va='bottom', 
            fontsize=12, fontweight='bold', color='red',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('各语言AntConc相似度对比.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("开始生成AntConc相似度对比图表...")
    
    # 创建详细对比图表
    print("1. 生成详细相似度对比图表...")
    create_similarity_comparison()
    
    # 创建按语言分组的对比图表
    print("2. 生成按语言分组的对比图表...")
    create_language_comparison()
    
    print("\n所有AntConc相似度对比图表已生成完成！")
    print("生成的图表文件：")
    print("- AntConc相似度专项对比图表.png")
    print("- 各语言AntConc相似度对比.png")

if __name__ == "__main__":
    main()
