#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AntConc文件加载问题
"""

import pandas as pd
from pathlib import Path

def test_load_antconc_file(file_path):
    """测试加载AntConc文件"""
    print(f"尝试加载文件: {file_path}")
    print(f"文件是否存在: {file_path.exists()}")
    
    if not file_path.exists():
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"文件行数: {len(lines)}")
        print("前5行内容:")
        for i, line in enumerate(lines[:5]):
            print(f"  {i+1}: {repr(line)}")
        
        # 解析数据
        data = []
        for line_num, line in enumerate(lines[1:], 2):  # 跳过标题行
            if line.strip():
                parts = line.strip().split('\t')
                print(f"第{line_num}行分割结果: {len(parts)}个部分: {parts[:6]}")
                
                if len(parts) >= 5:
                    word = parts[2]  # Headword列
                    rank = int(parts[3]) if parts[3].isdigit() else 0  # Rank列
                    freq = int(parts[4]) if parts[4].isdigit() else 0  # Freq列
                    
                    if word and freq > 0:
                        data.append({'word': word.lower(), 'rank': rank, 'frequency': freq})
                        if len(data) <= 5:  # 只打印前5个
                            print(f"  解析成功: {word} (rank={rank}, freq={freq})")
                
                if line_num > 10:  # 只检查前10行
                    break
        
        print(f"成功解析的词汇数: {len(data)}")
        return pd.DataFrame(data)
        
    except Exception as e:
        print(f"加载文件出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    base_dir = Path(".")
    
    # 测试三个语言的AntConc文件
    files_to_test = {
        "中文": base_dir / "中文分析" / "中文（anatonc）分析结果" / "1.Word_results.txt",
        "英文": base_dir / "英文分析" / "英文antconc分析结果" / "1.词频.txt",
        "德文": base_dir / "德文分析" / "德语（anatonc）分析" / "1.Word_results.txt"
    }
    
    for language, file_path in files_to_test.items():
        print(f"\n{'='*50}")
        print(f"测试{language}AntConc文件")
        print(f"{'='*50}")
        
        df = test_load_antconc_file(file_path)
        if df is not None and not df.empty:
            print(f"前10个高频词: {', '.join(df.head(10)['word'].tolist())}")

if __name__ == "__main__":
    main()
