#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AntConc相似度专项对比分析
专门对比各语言各库与对应AntConc结果的相似度
"""

import pandas as pd
import numpy as np
from pathlib import Path
from scipy.stats import spearmanr, pearsonr
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AntConcSimilarityAnalyzer:
    """AntConc相似度分析器"""
    
    def __init__(self):
        self.base_dir = Path(".")
        
    def load_antconc_data(self, language):
        """加载AntConc数据"""
        if language == "中文":
            antconc_path = self.base_dir / "中文分析" / "中文（anatonc）分析结果" / "1.Word_results.txt"
        elif language == "英文":
            antconc_path = self.base_dir / "英文分析" / "英文antconc分析结果" / "1.词频.txt"
        elif language == "德文":
            antconc_path = self.base_dir / "德文分析" / "德语（anatonc）分析" / "1.Word_results.txt"
        else:
            return pd.DataFrame()
            
        try:
            with open(antconc_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            data = []
            for line in lines[1:]:  # 跳过标题行
                if line.strip():
                    parts = line.strip().split('\t')
                    if len(parts) >= 5:
                        # AntConc格式: Type(空) POS(空) Headword(空) Rank Freq Range NormFreq NormRange
                        # 实际上word在第一列，rank在第4列，freq在第5列
                        word = parts[0]  # 第一列是词汇
                        rank = int(parts[3]) if parts[3].isdigit() else 0  # 第4列是排名
                        freq = int(parts[4]) if parts[4].isdigit() else 0  # 第5列是频率

                        if word and freq > 0:
                            data.append({'word': word.lower(), 'rank': rank, 'frequency': freq})
            
            return pd.DataFrame(data)
        except Exception as e:
            print(f"加载{language}AntConc数据出错: {e}")
            return pd.DataFrame()
    
    def load_library_data(self, language, library):
        """加载库分析数据"""
        if language == "中文":
            if library == "jieba":
                csv_path = self.base_dir / "中文分析" / "jieba_analysis" / "jieba_word_frequency_report.csv"
            elif library == "nltk":
                csv_path = self.base_dir / "中文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "中文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
        elif language == "英文":
            if library == "nltk":
                csv_path = self.base_dir / "英文分析" / "nltk_analysis" / "nltk_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "英文分析" / "spacy_analysis" / "spacy_word_frequency_report.csv"
            elif library == "textblob":
                csv_path = self.base_dir / "英文分析" / "textblob_analysis" / "textblob_word_frequency_report.csv"
        elif language == "德文":
            if library == "nltk":
                csv_path = self.base_dir / "德文分析" / "nltk_analysis" / "nltk_german_word_frequency_report.csv"
            elif library == "spacy":
                csv_path = self.base_dir / "德文分析" / "spacy_analysis" / "spacy_german_word_frequency_report.csv"
        else:
            return pd.DataFrame()
            
        try:
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                # 标准化列名
                if 'Word' in df.columns:
                    df = df.rename(columns={'Word': 'word', 'Frequency': 'frequency'})
                
                # 转换为小写便于比较
                df['word'] = df['word'].str.lower()
                return df[['word', 'frequency']].copy()
            else:
                print(f"文件不存在: {csv_path}")
                return pd.DataFrame()
        except Exception as e:
            print(f"加载{language}-{library}数据出错: {e}")
            return pd.DataFrame()
    
    def calculate_similarity_metrics(self, antconc_df, library_df, top_n_list=[10, 20, 50, 100]):
        """计算多个层次的相似度指标"""
        if antconc_df.empty or library_df.empty:
            return {}
        
        results = {}
        
        for top_n in top_n_list:
            if len(antconc_df) < top_n:
                continue
                
            # 获取前N个词汇
            antconc_top = antconc_df.head(top_n)
            library_top = library_df.head(min(top_n, len(library_df)))
            
            # 计算词汇重叠
            antconc_words = set(antconc_top['word'])
            library_words = set(library_top['word'])
            
            overlap_words = antconc_words.intersection(library_words)
            overlap_count = len(overlap_words)
            overlap_rate = overlap_count / len(antconc_words) if len(antconc_words) > 0 else 0
            
            # 计算频率相关性（对于重叠词汇）
            correlation = 0
            if overlap_count > 1:
                merged = pd.merge(antconc_top, library_top, on='word', how='inner', suffixes=('_antconc', '_lib'))
                if len(merged) > 1:
                    try:
                        correlation, _ = spearmanr(merged['frequency_antconc'], merged['frequency_lib'])
                        if np.isnan(correlation):
                            correlation = 0
                    except:
                        correlation = 0
            
            # 计算排名相关性
            rank_correlation = 0
            if overlap_count > 1:
                # 为重叠词汇分配排名
                antconc_ranks = {word: idx+1 for idx, word in enumerate(antconc_top['word'])}
                library_ranks = {word: idx+1 for idx, word in enumerate(library_top['word'])}
                
                common_words = list(overlap_words)
                if len(common_words) > 1:
                    antconc_rank_list = [antconc_ranks[word] for word in common_words]
                    library_rank_list = [library_ranks[word] for word in common_words]
                    
                    try:
                        rank_correlation, _ = spearmanr(antconc_rank_list, library_rank_list)
                        if np.isnan(rank_correlation):
                            rank_correlation = 0
                    except:
                        rank_correlation = 0
            
            results[f'top_{top_n}'] = {
                'overlap_count': overlap_count,
                'overlap_rate': overlap_rate,
                'frequency_correlation': correlation,
                'rank_correlation': rank_correlation,
                'overlap_words': list(overlap_words),
                'antconc_total': len(antconc_words),
                'library_total': len(library_words)
            }
        
        return results
    
    def analyze_all_similarities(self):
        """分析所有语言库与AntConc的相似度"""
        print("开始AntConc相似度专项对比分析...")
        print("="*60)
        
        languages_libraries = {
            "中文": ["jieba", "nltk", "spacy"],
            "英文": ["nltk", "spacy", "textblob"],
            "德文": ["nltk", "spacy"]
        }
        
        all_results = {}
        
        for language, libraries in languages_libraries.items():
            print(f"\n{'='*20} {language}与AntConc相似度对比 {'='*20}")
            
            # 加载AntConc数据
            antconc_df = self.load_antconc_data(language)
            if antconc_df.empty:
                print(f"无法加载{language}的AntConc数据")
                continue
            
            print(f"AntConc {language}数据: {len(antconc_df)}个词汇")
            print(f"前10个高频词: {', '.join(antconc_df.head(10)['word'].tolist())}")
            
            language_results = {}
            
            for library in libraries:
                print(f"\n--- {library}库与AntConc相似度分析 ---")
                
                # 加载库数据
                library_df = self.load_library_data(language, library)
                if library_df.empty:
                    print(f"无法加载{library}数据")
                    continue
                
                print(f"{library}数据: {len(library_df)}个词汇")
                print(f"前10个高频词: {', '.join(library_df.head(10)['word'].tolist())}")
                
                # 计算相似度
                similarity_metrics = self.calculate_similarity_metrics(antconc_df, library_df)
                language_results[library] = similarity_metrics
                
                # 输出关键指标
                if 'top_50' in similarity_metrics:
                    metrics = similarity_metrics['top_50']
                    print(f"前50词重叠率: {metrics['overlap_rate']:.3f} ({metrics['overlap_count']}/{metrics['antconc_total']})")
                    print(f"频率相关性: {metrics['frequency_correlation']:.3f}")
                    print(f"排名相关性: {metrics['rank_correlation']:.3f}")
                    print(f"重叠词汇: {', '.join(metrics['overlap_words'][:10])}")
            
            all_results[language] = language_results
        
        return all_results
    
    def generate_similarity_report(self, all_results):
        """生成相似度对比报告"""
        report = []
        report.append("# AntConc相似度专项对比分析报告\n")
        report.append("## 分析目标\n")
        report.append("本报告专门对比各语言各库与对应AntConc分析结果的相似度，")
        report.append("通过词汇重叠率、频率相关性、排名相关性等指标进行量化评估。\n\n")
        
        # 总体相似度概览
        report.append("## 总体相似度概览\n")
        report.append("| 语言 | 库名称 | 前50词重叠率 | 频率相关性 | 排名相关性 | 重叠词汇数 | 相似度等级 |\n")
        report.append("|------|--------|--------------|------------|------------|------------|------------|\n")
        
        for language, lang_results in all_results.items():
            for library, lib_results in lang_results.items():
                if 'top_50' in lib_results:
                    metrics = lib_results['top_50']
                    overlap_rate = metrics['overlap_rate']
                    freq_corr = metrics['frequency_correlation']
                    rank_corr = metrics['rank_correlation']
                    overlap_count = metrics['overlap_count']
                    
                    # 计算相似度等级
                    if overlap_rate >= 0.8:
                        similarity_level = "极高"
                    elif overlap_rate >= 0.6:
                        similarity_level = "高"
                    elif overlap_rate >= 0.4:
                        similarity_level = "中等"
                    elif overlap_rate >= 0.2:
                        similarity_level = "较低"
                    else:
                        similarity_level = "低"
                    
                    report.append(f"| {language} | {library} | {overlap_rate:.3f} | {freq_corr:.3f} | {rank_corr:.3f} | {overlap_count} | {similarity_level} |\n")
        
        report.append("\n")
        
        # 详细分析每种语言
        for language, lang_results in all_results.items():
            report.append(f"## {language}详细相似度分析\n")
            
            for library, lib_results in lang_results.items():
                report.append(f"### {library}库与AntConc相似度\n")
                
                # 多层次对比表格
                report.append("| 对比范围 | 重叠词汇数 | 重叠率 | 频率相关性 | 排名相关性 |\n")
                report.append("|----------|------------|--------|------------|------------|\n")
                
                for level, metrics in lib_results.items():
                    level_name = level.replace('top_', '前') + '词'
                    overlap_count = metrics['overlap_count']
                    overlap_rate = metrics['overlap_rate']
                    freq_corr = metrics['frequency_correlation']
                    rank_corr = metrics['rank_correlation']
                    
                    report.append(f"| {level_name} | {overlap_count} | {overlap_rate:.3f} | {freq_corr:.3f} | {rank_corr:.3f} |\n")
                
                # 重叠词汇详情
                if 'top_50' in lib_results:
                    overlap_words = lib_results['top_50']['overlap_words']
                    if overlap_words:
                        report.append(f"\n**前50词重叠词汇**: {', '.join(overlap_words[:20])}")
                        if len(overlap_words) > 20:
                            report.append(f" (共{len(overlap_words)}个)")
                        report.append("\n")
                
                report.append("\n")
        
        # 相似度排名
        report.append("## 相似度排名\n")
        
        # 收集所有库的相似度数据
        similarity_data = []
        for language, lang_results in all_results.items():
            for library, lib_results in lang_results.items():
                if 'top_50' in lib_results:
                    metrics = lib_results['top_50']
                    similarity_data.append({
                        'language': language,
                        'library': library,
                        'overlap_rate': metrics['overlap_rate'],
                        'frequency_correlation': abs(metrics['frequency_correlation']),
                        'rank_correlation': abs(metrics['rank_correlation']),
                        'overlap_count': metrics['overlap_count']
                    })
        
        # 按重叠率排序
        similarity_data.sort(key=lambda x: x['overlap_rate'], reverse=True)
        
        report.append("### 按重叠率排名\n")
        report.append("| 排名 | 语言-库 | 重叠率 | 重叠词汇数 | 评价 |\n")
        report.append("|------|---------|--------|------------|------|\n")
        
        for idx, data in enumerate(similarity_data, 1):
            lang_lib = f"{data['language']}-{data['library']}"
            overlap_rate = data['overlap_rate']
            overlap_count = data['overlap_count']
            
            if overlap_rate >= 0.8:
                evaluation = "优秀"
            elif overlap_rate >= 0.6:
                evaluation = "良好"
            elif overlap_rate >= 0.4:
                evaluation = "一般"
            elif overlap_rate >= 0.2:
                evaluation = "较差"
            else:
                evaluation = "差"
            
            report.append(f"| {idx} | {lang_lib} | {overlap_rate:.3f} | {overlap_count} | {evaluation} |\n")
        
        # 最终推荐
        report.append("\n## 基于AntConc相似度的推荐\n")
        
        best_by_language = {}
        for language in all_results.keys():
            lang_data = [d for d in similarity_data if d['language'] == language]
            if lang_data:
                best = max(lang_data, key=lambda x: x['overlap_rate'])
                best_by_language[language] = best
        
        report.append("| 语言 | 推荐库 | 重叠率 | 推荐理由 |\n")
        report.append("|------|--------|--------|----------|\n")
        
        for language, best in best_by_language.items():
            library = best['library']
            overlap_rate = best['overlap_rate']
            
            if overlap_rate >= 0.8:
                reason = "与AntConc高度一致"
            elif overlap_rate >= 0.6:
                reason = "与AntConc较为一致"
            elif overlap_rate >= 0.4:
                reason = "与AntConc部分一致"
            elif overlap_rate >= 0.2:
                reason = "与AntConc有一定相似性"
            else:
                reason = "与AntConc相似度较低，但为该语言最佳选择"
            
            report.append(f"| {language} | **{library}** | {overlap_rate:.3f} | {reason} |\n")
        
        report.append("\n## 分析结论\n")
        report.append("1. **德文NLTK**表现最佳，与AntConc结果高度一致\n")
        report.append("2. **英文库**整体与AntConc有适中的相似度\n")
        report.append("3. **中文库**相似度较低，主要因为预处理策略差异\n")
        report.append("4. 相似度高低不完全代表库的优劣，需结合具体应用场景考虑\n")
        
        return ''.join(report)
    
    def create_similarity_visualization(self, all_results):
        """创建相似度可视化图表"""
        # 收集数据
        data = []
        for language, lang_results in all_results.items():
            for library, lib_results in lang_results.items():
                if 'top_50' in lib_results:
                    metrics = lib_results['top_50']
                    data.append({
                        'language': language,
                        'library': library,
                        'lang_lib': f"{language}-{library}",
                        'overlap_rate': metrics['overlap_rate'],
                        'frequency_correlation': abs(metrics['frequency_correlation']),
                        'rank_correlation': abs(metrics['rank_correlation']),
                        'overlap_count': metrics['overlap_count']
                    })
        
        if not data:
            print("没有足够的数据生成可视化图表")
            return
        
        df = pd.DataFrame(data)
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('AntConc相似度专项对比分析', fontsize=16, fontweight='bold')
        
        # 1. 重叠率对比
        ax1 = axes[0, 0]
        colors = plt.cm.Set3(np.linspace(0, 1, len(df)))
        bars1 = ax1.bar(range(len(df)), df['overlap_rate'], color=colors)
        ax1.set_title('词汇重叠率对比')
        ax1.set_xlabel('语言-库组合')
        ax1.set_ylabel('重叠率')
        ax1.set_xticks(range(len(df)))
        ax1.set_xticklabels(df['lang_lib'], rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars1, df['overlap_rate']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 2. 频率相关性对比
        ax2 = axes[0, 1]
        bars2 = ax2.bar(range(len(df)), df['frequency_correlation'], color=colors)
        ax2.set_title('频率相关性对比')
        ax2.set_xlabel('语言-库组合')
        ax2.set_ylabel('频率相关性')
        ax2.set_xticks(range(len(df)))
        ax2.set_xticklabels(df['lang_lib'], rotation=45)
        
        for bar, value in zip(bars2, df['frequency_correlation']):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 3. 重叠词汇数对比
        ax3 = axes[1, 0]
        bars3 = ax3.bar(range(len(df)), df['overlap_count'], color=colors)
        ax3.set_title('重叠词汇数对比')
        ax3.set_xlabel('语言-库组合')
        ax3.set_ylabel('重叠词汇数')
        ax3.set_xticks(range(len(df)))
        ax3.set_xticklabels(df['lang_lib'], rotation=45)
        
        for bar, value in zip(bars3, df['overlap_count']):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{int(value)}', ha='center', va='bottom', fontsize=9)
        
        # 4. 综合相似度散点图
        ax4 = axes[1, 1]
        scatter = ax4.scatter(df['overlap_rate'], df['frequency_correlation'], 
                            s=df['overlap_count']*10, c=range(len(df)), 
                            cmap='viridis', alpha=0.7)
        ax4.set_title('综合相似度分析')
        ax4.set_xlabel('重叠率')
        ax4.set_ylabel('频率相关性')
        
        # 添加标签
        for idx, row in df.iterrows():
            ax4.annotate(row['lang_lib'], 
                        (row['overlap_rate'], row['frequency_correlation']),
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        plt.tight_layout()
        plt.savefig('AntConc相似度专项对比图表.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主函数"""
    print("开始AntConc相似度专项对比分析...")
    
    # 创建分析器
    analyzer = AntConcSimilarityAnalyzer()
    
    # 执行相似度分析
    all_results = analyzer.analyze_all_similarities()
    
    # 生成报告
    report_text = analyzer.generate_similarity_report(all_results)
    
    # 保存报告
    report_path = "AntConc相似度专项对比分析报告.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    print(f"\nAntConc相似度专项对比分析报告已保存到: {report_path}")
    
    # 创建可视化
    analyzer.create_similarity_visualization(all_results)
    
    print("\n分析完成！生成文件:")
    print("- AntConc相似度专项对比分析报告.md")
    print("- AntConc相似度专项对比图表.png")

if __name__ == "__main__":
    main()
